//
//  IMYPickConstants.h
//  IMYAssetsLibrary
//
//  Created by mario on 15/9/29.
//  Copyright © 2015年 meiyou. All rights reserved.
//

#ifndef IMYPickConstants_h
#define IMYPickConstants_h

#import <Foundation/Foundation.h>

typedef NS_ENUM(NSUInteger, IMYPickUploadStatus) {
    IMYPickUploadStatusNo = 0,
    IMYPickUploadStatusIn = 1,
    IMYPickUploadStatusFailed = 2,
    IMYPickUploadStatusYES = 3,
    IMYPickUploadStatusToSync = 4,
};

@protocol IMYPickUploadProtocol <NSObject>

@required
- (IMYPickUploadStatus)getUploadStatusForAssetURL:(NSURL *)url;

@end

@class IMYAssetModel;
@class IMYGridCell;

@protocol IMYPickUIProtocol <NSObject>

@optional
/// 设置 header 文案, 2 选 1
- (NSString *)groupTitleForDate:(NSDate *)date;

/// 设置 header 文案和文案 UI, 2 选 1
- (NSAttributedString *)groupAttributedTitleForDate:(NSDate *)date;

@required
- (void)gridCellAwakeFromNib:(IMYGridCell *)cell;

- (void)gridCell:(IMYGridCell *)cell setAsset:(IMYAssetModel *)asset;

@end

#endif /* IMYPickConstants_h */
