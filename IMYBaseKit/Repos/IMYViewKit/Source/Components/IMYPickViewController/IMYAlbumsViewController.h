//
//  IMYAlbumsViewController.h
//  YuEr
//
//  Created by mario on 15/7/3.
//  Copyright (c) 2015年 meiyou. All rights reserved.
//

#import <UIKit/UIKit.h>

@class IMYAssetsGroupModel;
@class IMYAlbumsViewController;

@protocol IMYAlbumsViewControllerDelegate <NSObject>

- (void)albumsViewController:(IMYAlbumsViewController *)albumsController didSelectAlbum:(IMYAssetsGroupModel *)album;
- (void)albumsViewControllerDismissed:(IMYAlbumsViewController *)albumsController;

@end


@interface IMYAlbumsViewController : UIViewController

@property (nonatomic, strong) NSArray<IMYAssetsGroupModel *> *albums;
@property (nonatomic, weak) id<IMYAlbumsViewControllerDelegate> delegate;

- (instancetype)initWithAlbums:(NSArray<IMYAssetsGroupModel *> *)albums;

- (void)dismissAction;

@end
