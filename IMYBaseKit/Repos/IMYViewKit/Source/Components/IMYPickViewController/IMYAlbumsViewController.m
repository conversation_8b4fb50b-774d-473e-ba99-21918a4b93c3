//
//  IMYAlbumsViewController.m
//  YuEr
//
//  Created by mario on 15/7/3.
//  Copyright (c) 2015年 meiyou. All rights reserved.
//

#import "IMYAlbumsViewController.h"
#import "IMYUserGroupModel.h"
#import "IMYAlbumCell.h"
#import "IMYViewKit.h"
#import "IMYPublic.h"

@interface IMYAlbumsViewController () <UITableViewDataSource, UITableViewDelegate>

@property (nonatomic, strong) UITableView *tableView;

@end

static NSString *CellIdentifier = @"IMYAlbumCell";

@implementation IMYAlbumsViewController

- (instancetype)initWithAlbums:(NSArray<IMYAssetsGroupModel *> *)albums {
    self = [super init];
    if (self) {
        self.albums = albums;
    }
    return self;
}

- (void)viewDidLoad {
    if (@available(iOS 13.0, *)) {
        // 在 iOS13 上强制为 UIUserInterfaceStyleLight (浅色，不跟随系统换肤)
        self.view.overrideUserInterfaceStyle = UIUserInterfaceStyleLight;
    }
    
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    [self setupTableView];
    //    self.view.alpha = 0.95;
    //增加蒙板 上面这样设置，子视图也会有透明度哦
    self.view.backgroundColor = [UIColor colorWithWhite:0.f alpha:0.5];
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
}

- (void)dismissAction {
    [self willMoveToParentViewController:nil];
    [self.view removeFromSuperview];
    [self removeFromParentViewController];
    [self didMoveToParentViewController:nil];

    if (self.delegate && [self.delegate respondsToSelector:@selector(albumsViewControllerDismissed:)]) {
        [self.delegate albumsViewControllerDismissed:self];
    }
}

- (void)setupTableView {
    self.tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
    self.tableView.layer.cornerRadius = 10;
    self.tableView.clipsToBounds = YES;
    self.tableView.delegate = self;
    self.tableView.dataSource = self;
    self.tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    [self.view addSubview:self.tableView];
    self.tableView.rowHeight = 44.0f;
    self.tableView.separatorInset = UIEdgeInsetsZero;
    CGFloat width = SCREEN_WIDTH - 35 * 2;
    CGFloat height = self.tableView.rowHeight * MIN(self.albums.count, 4.5);
    self.tableView.frame = CGRectMake((self.view.bounds.size.width - width) / 2, 10, width, height);
    [self.tableView imy_registerNibName:@"IMYAlbumCell" forCellReuseIdentifier:CellIdentifier];

    [self addInvisibleButton];

    UIImageView *arrowPop = [[UIImageView alloc] initWithImage:[UIImage imy_imageForKey:@"photo_popup"]];
    arrowPop.frame = CGRectMake(0, 0, 22, 10);
    arrowPop.imy_left = self.tableView.imy_centerX - arrowPop.imy_width / 2;
    arrowPop.imy_bottom = self.tableView.imy_top;
    [self.view addSubview:arrowPop];
}

- (void)addInvisibleButton {
    UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
    button.opaque = NO;
    [button addTarget:self action:@selector(dismissAction) forControlEvents:UIControlEventTouchUpInside];
    button.frame = self.view.bounds;
    [self.view insertSubview:button belowSubview:self.tableView];
}

#pragma mark - UITableView

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return 1;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.albums.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    IMYAlbumCell *cell = [tableView dequeueReusableCellWithIdentifier:CellIdentifier forIndexPath:indexPath];
    IMYAssetsGroupModel *album = self.albums[indexPath.row];
    cell.textLabel.text = album.title;
    cell.textLabel.font = [UIFont systemFontOfSize:16];
    cell.textLabel.textColor = IMY_COLOR_KEY(kIMY_Pink);
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    cell.selectedBackgroundView.backgroundColor = IMY_COLOR_KEY(kIMY_LightBlue);
    [cell imy_showLineForRow:indexPath.row leftMargin:15 rowCount:[self.albums count]];
    return cell;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    if ([self.delegate respondsToSelector:@selector(albumsViewController:didSelectAlbum:)]) {
        IMYAssetsGroupModel *album = self.albums[indexPath.row];
        [self.delegate albumsViewController:self didSelectAlbum:album];
        [self dismissAction];
    }
}

@end
