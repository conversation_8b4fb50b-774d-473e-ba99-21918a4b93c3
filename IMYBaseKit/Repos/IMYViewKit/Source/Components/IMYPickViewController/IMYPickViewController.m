//
//  IMYPickViewController.m
//  YuEr
//
//  Created by mario on 15/7/3.
//  Copyright (c) 2015年 meiyou. All rights reserved.
//

#import "IMYPickViewController.h"
#import "IMYAlbumsViewController.h"

#import "IMYGridViewController.h"
#import "IMYPublic.h"

#import "IMYAssetModel.h"
#import "IMYAssetsGroupModel.h"
#import "IMYAssetsManager.h"

#import <Masonry/Masonry.h>

NSUInteger kIMYPickViewControllerMaxPickCount = NSUIntegerMax;

@interface IMYPickViewController () <IMYAlbumsViewControllerDelegate, IMYGridViewControllerDataTarget, IMYGridViewControllerDataSource>

@property (nonatomic, strong) IMYGridViewController *gridController;
@property (nonatomic, strong) UIButton *titleButton;
@property (nonatomic, strong) NSArray<IMYAssetsGroupModel *> *albums;
@property (nonatomic, strong) NSMutableDictionary<NSString *,IMYAssetModel *> *targetAssetsDictionary;
@property (nonatomic, strong) NSMutableArray<IMYAssetModel *> *targetAssetsArray;

@end

@implementation IMYPickViewController

- (void)viewDidLoad {
    if (@available(iOS 13.0, *)) {
        // 在 iOS13 上强制为 UIUserInterfaceStyleLight (浅色，不跟随系统换肤)
        self.overrideUserInterfaceStyle = UIUserInterfaceStyleLight;
    }
    
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    self.targetAssetsDictionary = [NSMutableDictionary dictionaryWithCapacity:0];
    self.targetAssetsArray = [NSMutableArray arrayWithCapacity:0];

    self.gridController = [[IMYGridViewController alloc] init];
    self.gridController.markerType = self.markerType;
    self.gridController.hiddenAllSelectItem = self.hiddenAllSelectItem;
    self.gridController.dataTarget = self;
    self.gridController.dataSource = self;
    [self.gridController willMoveToParentViewController:self];
    [self.view addSubview:self.gridController.view];
    self.gridController.view.frame = self.view.bounds;
    [self addChildViewController:self.gridController];
    [self.gridController didMoveToParentViewController:self];
    @weakify(self);
    [self.gridController.view mas_makeConstraints:^(MASConstraintMaker *make) {
        @strongify(self);
        make.edges.equalTo(self.view).with.insets(UIEdgeInsetsMake(0, 0, 0, 0));
    }];
    if (self.edgesForExtendedLayout == UIRectEdgeAll) {
        self.automaticallyAdjustsScrollViewInsets = NO;
    }
    [self loadContent];
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
}

- (void)loadContent {
    @weakify(self);
    dispatch_async(dispatch_get_global_queue(0, 0), ^{
        @strongify(self);
        NSMutableArray *albumsArray = [[NSMutableArray alloc] init];
        NSArray<PHAssetCollection *> *photoCollections =
        [IMYAssetsManager fetchAllAlbumsWithAlbumContentType:IMYAssetsContentTypePhotoAndVideo
                                              showEmptyAlbum:NO
                                              showSmartAlbum:NO];
        
        PHFetchOptions *options = [IMYAssetsManager createFetchOptionsWithAlbumContentType:IMYAssetsContentTypePhotoAndVideo];
        for (NSUInteger i = 0; i < photoCollections.count; i++) {
            PHFetchOptions *albumOptions = [options copy];
            IMYAssetsGroupModel *album = [[IMYAssetsGroupModel alloc] initWithPHCollection:photoCollections[i]
                                                                        fetchAssetsOptions:albumOptions];
            [album reload];
            [albumsArray addObject:album];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            [self reloadWithAlbums:albumsArray];
        });
    });
}

- (void)setupTitleViewWithAlbum:(IMYAssetsGroupModel *)album {
    NSString *title = album ? album.title : @"照片";
    self.titleButton = [UIButton buttonWithType:UIButtonTypeCustom];
    self.titleButton.titleLabel.font = [UIFont systemFontOfSize:20];
    [self.titleButton imy_setTitleColorForKey:kCK_Black_A andState:UIControlStateNormal];
    [self.titleButton setTitle:title forState:UIControlStateNormal];
    if ([self.albums count] > 1) {
        [self.titleButton imy_setImageForKey:@"all_top_down" andState:UIControlStateNormal];
        [self.titleButton addTarget:self action:@selector(titleButtonTapAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    CGSize textSize = [title sizeWithFont:self.titleButton.titleLabel.font];
    CGFloat space = 5.0f;
    self.titleButton.imageEdgeInsets = UIEdgeInsetsMake(0, textSize.width + space, 0, -space);
    self.titleButton.titleEdgeInsets = UIEdgeInsetsMake(0, -self.titleButton.imageView.image.size.width, 0, self.titleButton.imageView.image.size.width);
    [self.titleButton sizeToFit];
    self.titleButton.imy_centerX = SCREEN_WIDTH / 2;
    self.titleButton.imy_centerY = 44 / 2;
    if (self.markerType == IMYGridCellMarkerTypeVideo) {
        self.title = IMYString(@"视频");
    } else {
        self.parentViewController.navigationItem.titleView = self.titleButton;
    }
}

- (void)titleButtonTapAction:(id)sender {
    [IMYEventHelper event:@"sczpy-wdxc"];
    IMYAlbumsViewController *albumsController = [self getAlbumsViewController];
    if (albumsController) {
        [albumsController dismissAction];
    } else {
        if (self.albums) {
            if ([self.albums count] > 1) {
                [self.titleButton imy_setImageForKey:@"all_top_up" andState:UIControlStateNormal];
            }
            albumsController = [[IMYAlbumsViewController alloc] initWithAlbums:self.albums];
            albumsController.delegate = self;
            [albumsController willMoveToParentViewController:self];
            [self.view addSubview:albumsController.view];
            @weakify(self);
            [albumsController.view mas_makeConstraints:^(MASConstraintMaker *make) {
                @strongify(self);
                //                make.edges.equalTo(self.view).with.insets(UIEdgeInsetsMake(5, 0, -5, 0));
                make.edges.equalTo(self.view);
            }];
            [self addChildViewController:albumsController];
            [albumsController didMoveToParentViewController:self];
        }
    }
}

- (IMYAlbumsViewController *)getAlbumsViewController {
    for (UIViewController *controller in self.childViewControllers) {
        if ([controller isKindOfClass:[IMYAlbumsViewController class]]) {
            return (IMYAlbumsViewController *)controller;
        }
    }
    return nil;
}

- (void)reloadWithAlbums:(NSArray *)albums {
    if (albums != self.albums) {
        self.albums = albums;
        IMYAssetsGroupModel *album = [albums firstObject];
        [self updateWithAlbum:album];
        if (albums.count > 0) {
            [self hideAuxiliaryView];
        } else {
            [self showAuxiliaryView];
        }
    }
}

- (void)updateWithAlbum:(IMYAssetsGroupModel *)album {
    [self setupTitleViewWithAlbum:album];
    self.gridController.album = album;
    if (self.dateToScroll) {
        [self.gridController scrollToDate:self.dateToScroll animated:NO];
        self.dateToScroll = nil;
    }
}

- (void)hideAuxiliaryView {
}

- (void)showAuxiliaryView {
}

- (NSUInteger)maxUploadCount {
    if (self.delegate && [self.delegate respondsToSelector:@selector(maxCountInPickViewController:)]) {
        return [self.delegate maxCountInPickViewController:self];
    }
    return kIMYPickViewControllerMaxPickCount;
}

#pragma mark - IMYAlbumsViewControllerDelegate

- (void)albumsViewController:(IMYAlbumsViewController *)albumsController didSelectAlbum:(IMYAssetsGroupModel *)album {
    [self updateWithAlbum:album];
}

- (void)albumsViewControllerDismissed:(IMYAlbumsViewController *)albumsController {
    if ([self.albums count] > 1) {
        [self.titleButton imy_setImageForKey:@"all_top_down" andState:UIControlStateNormal];
    }
}

#pragma mark - IMYGridViewControllerDataSource

- (NSInteger)numberOfItemInRowInController:(IMYGridViewController *)controller {
    if (self.markerType == IMYGridCellMarkerTypeYunqi || self.markerType == IMYGridCellMarkerTypeXiangCe || self.markerType == IMYGridCellMarkerTypeVideo) {
        return 4;
    }
    return 0;
}

#pragma mark - IMYGridViewControllerDataTarget
//录制视频调用
- (void)qpCameraAction {
    if ([self.delegate respondsToSelector:@selector(qpCameraAction)]) {
        [self.delegate qpCameraAction];
    }
}

//相册视频预览
- (void)previewAlbumVideo:(IMYAssetModel *)asset {
    if ([self.delegate respondsToSelector:@selector(previewAlbumVideo:)]) {
        [self.delegate previewAlbumVideo:asset];
    }
}

- (BOOL)addAsset:(IMYAssetModel *)asset {
    if (!asset || !asset.identifier) {
        return NO;
    }

    NSUInteger maxCount = [self maxUploadCount];
    if ([self.targetAssetsDictionary count] < maxCount) {
        if ([self.targetAssetsArray containsObject:asset]) {
            return YES;
        }
        [self.targetAssetsDictionary setValue:asset forKey:asset.identifier];
        [self.targetAssetsArray addObject:asset];
        if ([self.delegate respondsToSelector:@selector(pickViewControllerDidUpdated:)]) {
            [self.delegate pickViewControllerDidUpdated:self];
        }
        return YES;
    }
    //    [UIView imy_showTextHUD:[NSString stringWithFormat:[IMYNetState isWWAN] ? IMYString(@"当前为非WIFI环境，最多可以选择%lu张照片哦") : IMYString(@"最多可以选择%lu张照片哦"), maxCount]];
    if (self.markerType != IMYGridCellMarkerTypeVideo) {
        [UIView imy_showTextHUD:[NSString stringWithFormat:IMYString(@"最多只能选%lu张照片哦～"), maxCount]];
    }
    return NO;
}

- (void)removeAsset:(IMYAssetModel *)asset {
    if (!asset || !asset.identifier) {
        return;
    }

    [self.targetAssetsDictionary removeObjectForKey:asset.identifier];
    [self.targetAssetsArray removeObject:asset];
    if ([self.delegate respondsToSelector:@selector(pickViewControllerDidUpdated:)]) {
        [self.delegate pickViewControllerDidUpdated:self];
    }
}

- (void)removeAllAsset {
    [self.targetAssetsDictionary removeAllObjects];
    [self.targetAssetsArray removeAllObjects];
    if ([self.delegate respondsToSelector:@selector(pickViewControllerDidUpdated:)]) {
        [self.delegate pickViewControllerDidUpdated:self];
    }
}

- (BOOL)containsAsset:(IMYAssetModel *)asset {
    id containsValue = [self.targetAssetsDictionary objectForKey:asset.identifier];
    if (containsValue) {
        return YES;
    }
    return NO;
}

- (NSInteger)assetsCount {
    return self.targetAssetsDictionary.count;
}

#pragma mark - Public

- (NSArray *)selectedAssets {
    return self.targetAssetsArray;
}

@end
