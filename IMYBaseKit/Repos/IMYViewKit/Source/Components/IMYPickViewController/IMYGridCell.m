//
//  IMYGridCell.m
//  YuEr
//
//  Created by mario on 15/7/3.
//  Copyright (c) 2015年 meiyou. All rights reserved.
//

#import "IMYGridCell.h"
#import "IMYPickConfig.h"
#import "IMYPublic.h"
#import <Masonry/Masonry.h>

#import "IMYAssetModel.h"

@interface IMYGridCell ()

@property (weak, nonatomic) IBOutlet UIView *myMaskView;
@property (weak, nonatomic) IBOutlet UILabel *hasUploadLabel;
@property (weak, nonatomic) IBOutlet UIImageView *statusImageView;
@property (strong, nonatomic) UIImageView *markerIconImageView; //除默认类型外，选中状态的状态图片框
@property (weak, nonatomic) IBOutlet UILabel *videoTimeLabel;

@property (assign, nonatomic) IMYPickUploadStatus status;

@end

@implementation IMYGridCell

- (void)awakeFromNib {
    // Initialization code

    // fix iOS7
    self.contentView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;

    id<IMYPickUIProtocol> delegate = [IMYPickConfig defaultConfig].pickUIDelegate;
    if ([delegate respondsToSelector:@selector(gridCellAwakeFromNib:)]) {
        [delegate gridCellAwakeFromNib:self];
    }
    self.indicatorView.hidden = (self.markerType == IMYGridCellMarkerTypeVideo);
    self.videoInfoBG.hidden = !(self.markerType == IMYGridCellMarkerTypeVideo);
    //    [self.contentView mb_addRoundedCorner];
    //    self.mediaView.backgroundColor = IMY_COLOR_KEY(kIMY_LightGrey);
    //    self.mediaView.placeholderImageName = @"time_default";
    //    self.mediaView.failureImageName = @"time_failure";
    //    [self.indicatorView imy_setImage:@"all_choice" highl:nil stretch:YES];
    //    [self.contentView bringSubviewToFront:self.indicatorView];
    //    self.mediaView.contentMode = UIViewContentModeScaleAspectFill;
    self.selected = NO;
    self.status = IMYPickUploadStatusNo;
    self.hasUploadBG.backgroundColor = [UIColor imy_colorForKey:kIMY_Pink];
    //    @weakify(self);
    //    [[[[[NSNotificationCenter defaultCenter] rac_addObserverForName:IMYPickUploadStatusChanged object:nil] takeUntil:self.rac_willDeallocSignal] deliverOnMainThread] subscribeNext:^(NSNotification *assetURLStringObject) {
    //        @strongify(self);
    //        NSString *assetURLString = assetURLStringObject.object;
    //        if ([self.asset.url.absoluteString isEqualToString:assetURLString]) {
    //            [self updateHasUploadLabel];
    //        }
    //    }];
}

- (void)setMarked:(BOOL)marked {
    _marked = marked;
    if (_markerType == IMYGridCellMarkerTypeYuer) {
        self.myMaskView.hidden = self.indicatorView.hidden = !marked;
    } else if (_markerType == IMYGridCellMarkerTypeYunqi) {
        NSString *image = _marked ? @"image_chose_up" : @"image_chose";
        [self.markerIconImageView imy_setImage:image];
    } else if (_markerType == IMYGridCellMarkerTypeXiangCe) {
        self.myMaskView.hidden = !marked;
        NSString *image = _marked ? @"photo_icon_choice" : @"photo_icon_nochoice";
        [self.markerIconImageView imy_setImage:image];
    }
}

- (void)setAsset:(IMYAssetModel *)asset {
    if (_asset != asset) {
        _asset = asset;
        id<IMYPickUIProtocol> delegate = [IMYPickConfig defaultConfig].pickUIDelegate;
        if ([delegate respondsToSelector:@selector(gridCell:setAsset:)]) {
            [delegate gridCell:self setAsset:asset];
        } else {
            self.mediaView.image = asset.originImage;
        }
        //        [self.mediaView mb_setImageWithAssetOrURL:asset quality:IMYAssetQualityThumbnail];
    }
    [self updateHasUploadLabel];
}

- (void)updateHasUploadLabel {
    id<IMYPickUploadProtocol> delegate = [IMYPickConfig defaultConfig].uploadDelegate;
    if ([delegate respondsToSelector:@selector(getUploadStatusForAssetURL:)]) {
        IMYPickUploadStatus status = [delegate getUploadStatusForAssetURL:self.asset.identifier];
        self.status = status;
        self.hasUploadBG.hidden = self.hasUploadLabel.hidden = (status == IMYPickUploadStatusNo);
        //        self.statusImageView.hidden = (status == IMYPickUploadStatusNo);
        self.statusImageView.hidden = YES;
        //        self.hasUploadLabel.hidden = YES;
        if (_markerType == IMYGridCellMarkerTypeXiangCe) {
            //            self.markerIconImageView.hidden = YES;
            self.hasUploadBG.hidden = YES;
            self.hasUploadLabel.hidden = status != IMYPickUploadStatusYES;
            [self.hasUploadLabel mas_updateConstraints:^(MASConstraintMaker *make) {
                make.top.mas_equalTo(self.contentView.mas_top);
            }];
        }
        if (_markerType == IMYGridCellMarkerTypeVideo) {
            self.markerIconImageView.hidden = YES;
            self.hasUploadBG.hidden = YES;
            self.videoInfoBG.hidden = NO;
            //self.videoTimeLabel.text = [self timeFormatted:self.asset.videoDuration];
            self.videoTimeLabel.text = self.asset.duration;
        }
        if (status == IMYPickUploadStatusIn) {
            self.hasUploadLabel.text = IMYString(@"上传中");
            [self.statusImageView imy_setImage:@"upload_in"];
        } else if (status == IMYPickUploadStatusYES) {
            self.hasUploadLabel.text = IMYString(@"已上传");
            [self.statusImageView imy_setImage:@"upload_complete"];
        } else if (status == IMYPickUploadStatusFailed) {
            self.hasUploadLabel.text = IMYString(@"上传中");
            [self.statusImageView imy_setImage:@"upload_in"];
        } else if (status == IMYPickUploadStatusToSync) {
            self.hasUploadLabel.text = IMYString(@"同步中");
            [self.statusImageView imy_setImage:@"upload_synchronous"];
        } else {
            self.hasUploadLabel.text = @"";
            if (_markerType == IMYGridCellMarkerTypeXiangCe) {
                self.markerIconImageView.hidden = NO;
            }
        }
    }
}

- (NSString *)timeFormatted:(double)totalSeconds {
    NSTimeInterval timeInterval = totalSeconds;
    long seconds = timeInterval;
    int hour = 0;
    int minute = seconds / 60.0f;
    int second = seconds % 60;
    if (minute > 59) {
        hour = minute / 60;
        minute = minute % 60;
        return [NSString stringWithFormat:@"%02d:%02d:%02d", hour, minute, second];
    } else {
        return [NSString stringWithFormat:@"%02d:%02d", minute, second];
    }
}

- (BOOL)uploaded {
    if (self.status == IMYPickUploadStatusNo) {
        return NO;
    }
    return YES;
}

- (void)showAnimation {
    UIView *markerView = self.indicatorView;
    if (_marked != IMYGridCellMarkerTypeYuer) {
        markerView = self.markerIconImageView;
    }

    if (markerView.hidden) {
        return;
    }
    CAKeyframeAnimation *anim = [CAKeyframeAnimation animationWithKeyPath:@"transform"];
    double duration = 0.15 + 0.12 + 0.09;
    anim.duration = duration;
    NSValue *v1 = [NSValue valueWithCATransform3D:CATransform3DMakeScale(1, 1, 1)];
    NSValue *v2 = [NSValue valueWithCATransform3D:CATransform3DMakeScale(1.2, 1.2, 1)];
    NSValue *v3 = [NSValue valueWithCATransform3D:CATransform3DMakeScale(0.9, 0.9, 1)];
    NSValue *v4 = [NSValue valueWithCATransform3D:CATransform3DMakeScale(1, 1, 1)];
    anim.keyTimes =
        @[@0, @(0.15 / duration), @((0.15 + 0.12) / duration), @((0.15 + 0.12 + 0.09) / duration)];
    anim.values = @[v1, v2, v3, v4];
    [markerView.layer addAnimation:anim forKey:@"111"];
}

- (void)setMarkerType:(IMYGridCellMarkerType)markerType {
    if (_markerType == markerType) {
        return;
    }

    _markerType = markerType;
    if (markerType == IMYGridCellMarkerTypeYuer) {
        //这种类型是默认的
    } else if (markerType == IMYGridCellMarkerTypeYunqi) {

        [self.indicatorView removeFromSuperview];
        self.indicatorView = nil;

        self.markerIconImageView = [[UIImageView alloc] init];
        [self.markerIconImageView imy_setImage:@"image_chose"];
        [self.contentView addSubview:self.markerIconImageView];
        [self.markerIconImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(self.mas_right).offset(-5);
            make.top.equalTo(@(5));
            make.width.equalTo(@20);
            make.height.equalTo(@20);
        }];
    } else if (markerType == IMYGridCellMarkerTypeXiangCe) {

        [self.indicatorView removeFromSuperview];
        self.indicatorView = nil;

        self.markerIconImageView = [[UIImageView alloc] init];
        [self.markerIconImageView imy_setImage:@"photo_nochoice"];
        [self.contentView addSubview:self.markerIconImageView];
        [self.markerIconImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(self.mas_right).offset(-5);
            make.bottom.equalTo(self.mas_bottom).offset(-5);
            make.width.equalTo(@24);
            make.height.equalTo(@24);
        }];
    } else if (markerType == IMYGridCellMarkerTypeVideo) {
        [self.indicatorView removeFromSuperview];
        self.indicatorView = nil;
        self.videoInfoBG.hidden = NO;
    }
}

@end
