//
//  IMYGridViewController.h
//  YuEr
//
//  Created by mario on 15/7/3.
//  Copyright (c) 2015年 meiyou. All rights reserved.
//

#import "IMYGridCell.h"
#import <UIKit/UIKit.h>

@class IMYAssetModel;
@class IMYAssetsGroupModel;
@class IMYGridViewController;
@class IMYGridGroup;

@protocol IMYGridViewControllerDataTarget <NSObject>

@required
- (NSInteger)assetsCount;

- (void)removeAsset:(IMYAssetModel *)asset;

- (void)removeAllAsset;

- (BOOL)addAsset:(IMYAssetModel *)asset;

- (BOOL)containsAsset:(IMYAssetModel *)asset;

- (NSMutableArray<IMYGridGroup *> *)sortAssets:(NSMutableArray<IMYAssetModel *> *)assets;

@optional
//育儿录制视频调用
- (void)qpCameraAction;

//育儿预览相册视频
- (void)previewAlbumVideo:(IMYAssetModel *)asset;

@end

@protocol IMYGridViewControllerDataSource <NSObject>

//每行的数量
- (NSInteger)numberOfItemInRowInController:(IMYGridViewController *)controller;

@end

@interface IMYGridGroup : NSObject

@property (nonatomic, strong) NSDate *date;
@property (nonatomic, strong) NSMutableArray<IMYAssetModel *> *assets;
@property (nonatomic, assign) BOOL selected;

@end

@interface IMYGridViewController : UIViewController

@property (nonatomic, strong) IMYAssetsGroupModel *album;
@property (nonatomic, weak) id<IMYGridViewControllerDataTarget> dataTarget;
@property (nonatomic, weak) id<IMYGridViewControllerDataSource> dataSource;
@property (nonatomic, assign) BOOL hiddenAllSelectItem;
@property (nonatomic, assign) IMYGridCellMarkerType markerType;

- (void)reloadData;
- (BOOL)scrollToDate:(NSDate *)date animated:(BOOL)animated;

@end
