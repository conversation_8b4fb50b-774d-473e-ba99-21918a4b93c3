//
//  IMYGridViewController.m
//  YuEr
//
//  Created by mario on 15/7/3.
//  Copyright (c) 2015年 meiyou. All rights reserved.
//

#import "IMYGridViewController.h"
#import "IMYAssetsManager.h"
#import "IMYCaptionView.h"
#import "IMYGridCell.h"
#import "IMYGridHeader.h"
#import "IMYPickConfig.h"
#import "IMYPublic.h"
#import <Masonry/Masonry.h>
#import <ReactiveCocoa/ReactiveCocoa.h>
#import "IMYViewKit.h"

@implementation IMYGridGroup

@end


@interface IMYGridViewController () <UICollectionViewDataSource, UICollectionViewDelegate, PHPhotoLibraryChangeObserver>

@property (nonatomic, assign) NSInteger operationSize;

@property (nonatomic, strong) UICollectionView *collectionView;
@property (nonatomic, strong) NSMutableArray<IMYAssetModel *> *assets;
@property (nonatomic, strong) NSMutableArray<IMYGridGroup *> *sortedGroups;
@property (nonatomic, strong) NSMutableDictionary *headerDict;
@property (nonatomic, strong) IMYCaptionView *captionView;
@property (nonatomic, strong) UIView *topQPCameraView;

/** 毛玻璃view */
@property (nonatomic, strong) UIView *topBlurView;
/** 毛玻璃左部显示日期的label */
@property (nonatomic, strong) UILabel *topLeftDateLabel;
/** 毛玻璃右部显示日期的'全选'按钮 */
@property (nonatomic, strong) UIButton *topRightSelectButton;
/** 毛玻璃view当前位置对应的选择的相册section */
@property (nonatomic, strong) IMYGridGroup *sectionGroup;
/** 存放计算好的每个section底部的Y坐标值 */
@property (nonatomic, strong) NSMutableArray<NSNumber *> *sectionArrayAxisY;

@end

@implementation IMYGridViewController

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
    [[PHPhotoLibrary sharedPhotoLibrary] unregisterChangeObserver:self];
}

- (instancetype)initWithNibName:(NSString *)nibNameOrNil bundle:(NSBundle *)nibBundleOrNil {
    self = [super initWithNibName:nibNameOrNil bundle:nibBundleOrNil];
    if (self) {
        self.assets = [NSMutableArray array];
        self.headerDict = [NSMutableDictionary dictionary];
    }
    return self;
}

- (void)viewDidLoad {
    if (@available(iOS 13.0, *)) {
        // 在 iOS13 上强制为 UIUserInterfaceStyleLight (浅色，不跟随系统换肤)
        self.overrideUserInterfaceStyle = UIUserInterfaceStyleLight;
    }

    [super viewDidLoad];
    // Do any additional setup after loading the view.

    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(handleApplicationDidBecomeActive) name:UIApplicationDidBecomeActiveNotification object:nil];
    [self createUI];
    
    const IMYAssetAuthorizationStatus status = [IMYAssetsManager authorizationStatus];
    if (status == IMYAssetAuthorizationStatusAuthorized ||
        status == IMYAssetAuthorizationStatusLimited) {
        [[PHPhotoLibrary sharedPhotoLibrary] registerChangeObserver:self];
    } else if (status == IMYAssetAuthorizationStatusNotDetermined) {
        [IMYAssetsManager requestAuthorizationForAccessLevel:2 handler:^(IMYAssetAuthorizationStatus const status) {
            if (status == IMYAssetAuthorizationStatusAuthorized ||
                status == IMYAssetAuthorizationStatusLimited) {
                [[PHPhotoLibrary sharedPhotoLibrary] registerChangeObserver:self];
            }
        }];
    }
}

// This callback is invoked on an arbitrary serial queue. If you need this to be handled on a specific queue, you should redispatch appropriately
- (void)photoLibraryDidChange:(PHChange *)changeInstance {
    imy_asyncMainExecuteBlock(^{
        if (!self.album) {
//            [[[[IMYDaduService utilsService] loadAlbum] deliverOnMainThread] subscribeNext:^(id album) {
//                self.album = album;
//                [self reloadAlbum];
//            }];
        } else {
            [self reloadAlbum];
        }
    });
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
}

- (void)handleApplicationDidBecomeActive {
    //育儿1.1注释掉，从后台进入会刷新
    //    [self reloadAlbum];
}

- (void)handleQPCameraAction {
    if ([self.dataTarget respondsToSelector:@selector(qpCameraAction)]) {
        [self.dataTarget qpCameraAction];
    }
}

- (void)addTopCameraViewWithHeight:(CGFloat)height {
    self.collectionView.contentInset = UIEdgeInsetsMake(height, 0, 0, 0);
    self.topQPCameraView = [[UIView alloc] initWithFrame:CGRectMake(0, -height, SCREEN_WIDTH, height)];
    self.topQPCameraView.backgroundColor = [UIColor whiteColor];

    UIImageView *blurImageView = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"move_shoot_image"]];
    blurImageView.frame = CGRectMake(0, 0, height, height);
    [self.topQPCameraView addSubview:blurImageView];

    UIButton *qpButton = [UIButton buttonWithType:UIButtonTypeCustom];
    qpButton.frame = CGRectMake(0, 0, height, height);
    [qpButton imy_setImage:@"ic_shooting_in"];
    @weakify(self);
    [[[qpButton rac_signalForControlEvents:UIControlEventTouchUpInside] throttle:0.3] subscribeNext:^(id x) {
        @strongify(self);
        [self handleQPCameraAction];
    }];
    [self.topQPCameraView addSubview:qpButton];

    [self.collectionView addSubview:self.topQPCameraView];
    self.topQPCameraView.hidden = YES;
}

- (void)createUI {
    UICollectionViewFlowLayout *layout = [[UICollectionViewFlowLayout alloc] init];
    self.collectionView = [[UICollectionView alloc] initWithFrame:self.view.bounds collectionViewLayout:layout];
    [self.view addSubview:self.collectionView];
    @weakify(self);
    [self.collectionView mas_makeConstraints:^(MASConstraintMaker *make) {
        @strongify(self);
        make.edges.equalTo(self.view).with.insets(UIEdgeInsetsMake(0, 0, 0, 0));
    }];
    self.collectionView.delegate = self;
    self.collectionView.dataSource = self;
    self.collectionView.showsVerticalScrollIndicator = NO;
    [self.collectionView imy_makeTransparent];
    
    [self.collectionView registerClass:UICollectionViewCell.class forCellWithReuseIdentifier:@"TaskCell"];
    [self.collectionView registerClass:UICollectionReusableView.class forSupplementaryViewOfKind:UICollectionElementKindSectionHeader withReuseIdentifier:@"TaskHeader"];
    [self.collectionView imy_registerNibName:@"IMYGridCell" forCellReuseIdentifier:@"IMYGridCell"];
    [self.collectionView imy_registerNibName:@"IMYGridHeader" forSupplementaryViewOfKind:UICollectionElementKindSectionHeader withReuseIdentifier:@"IMYGridHeader"];

    NSUInteger itemInRow = 0;
    if (self.dataSource && [self.dataSource respondsToSelector:@selector(numberOfItemInRowInController:)]) {
        itemInRow = [self.dataSource numberOfItemInRowInController:self];
    }
    CGFloat topViewHeight = 0;
    //布局
    if (itemInRow > 0) {
        CGFloat spacing = 1;
        layout.minimumInteritemSpacing = spacing;
        layout.minimumInteritemSpacing = spacing;
        layout.sectionInset = UIEdgeInsetsMake(0, 0, 4, 0);
        layout.headerReferenceSize = CGSizeMake(self.view.imy_width, 42);
        NSInteger itemCount = [self.dataSource numberOfItemInRowInController:self];
        CGFloat width = (self.view.imy_width - (itemCount - 1) * spacing) / itemCount;
        layout.itemSize = CGSizeMake(width, width);
        topViewHeight = width;
    } else {
        //默认
        layout.minimumLineSpacing = 2;
        layout.minimumInteritemSpacing = 2;
        layout.sectionInset = UIEdgeInsetsMake(0, 10, 4, 10);
        layout.headerReferenceSize = CGSizeMake(self.view.imy_width, 42);
        CGFloat width = (self.view.imy_width - 28) / 3;
        layout.itemSize = CGSizeMake(width, width);
    }
    //    if (self.markerType == IMYGridCellMarkerTypeVideo) {
    //        [self addTopCameraViewWithHeight:topViewHeight];
    //    }
    self.captionView = [IMYCaptionView addToView:self.view show:NO];
    self.captionView.allTapToRetry = YES;
    [self.captionView setTitle:IMYString(self.markerType == IMYGridCellMarkerTypeVideo ? @"无视频" : @"无照片") forState:IMYCaptionViewStateNoResult];
    self.captionView.retryBlock = ^{
        @strongify(self);
        [self reloadAlbum];
    };

    // 添加置顶的毛玻璃view....模拟系统相册 ++
    [self configBlurView];
}

// 添加置顶的毛玻璃view....模拟系统相册 ++
- (void)configBlurView {

    self.topBlurView = [[UIView alloc] init];
    self.topBlurView.frame = CGRectMake(0, 0, self.view.imy_width, 34);
    [self.view addSubview:self.topBlurView];
    [self becomeToBlurView:self.topBlurView];
    self.topBlurView.hidden = YES;

    self.topLeftDateLabel = [[UILabel alloc] init];
    self.topLeftDateLabel.text = @"8个月3天 1月14日";
    self.topLeftDateLabel.font = [UIFont systemFontOfSize:13];
    self.topLeftDateLabel.textColor = IMY_COLOR_KEY(kIMY_Grey);
    [self.topBlurView addSubview:self.topLeftDateLabel];
    // 设置位置
    [self.topLeftDateLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@10);
        make.bottom.equalTo(@(-7));
    }];

    if (self.markerType != IMYGridCellMarkerTypeVideo) {
        if (!self.hiddenAllSelectItem) {
            self.topRightSelectButton = [[UIButton alloc] init];
            [self.topRightSelectButton imy_setTitleColor:kIMY_Pink];
            [self.topRightSelectButton setTitle:@"全选" forState:UIControlStateNormal];
            self.topRightSelectButton.titleLabel.font = [UIFont systemFontOfSize:16];
            [self.topRightSelectButton addTarget:self action:@selector(selectAllPhotosAction) forControlEvents:UIControlEventTouchUpInside];
            [self.topBlurView addSubview:self.topRightSelectButton];
            // 设置位置
            @weakify(self)
                [self.topRightSelectButton mas_makeConstraints:^(MASConstraintMaker *make) {
                    @strongify(self)
                        make.right.equalTo(@(-10));
                    make.centerY.equalTo(self.topLeftDateLabel);
                }];
        }
    }
}

- (void)selectGroup:(IMYGridGroup *)group {
    [IMYEventHelper event:@"sczpy-qx"];
    if (!group.selected) {
        NSInteger i = 0;
        for (; i < [group.assets count]; ++i) {
            IMYAssetModel *asset = group.assets[i];
            BOOL uploaded = [self getUploadedForAssetURL:asset.identifier];
            if (self.markerType == IMYGridCellMarkerTypeXiangCe) {
                uploaded = NO;
            }
            if (!uploaded && ![self selectAsset:asset]) {
                break;
            }
        }
        if (i == [group.assets count]) {
            group.selected = !group.selected;
        }
    } else {
        for (IMYAssetModel *asset in group.assets) {
            [self deselectAsset:asset];
        }
        group.selected = !group.selected;
    }
    [self reloadData];
}

- (void)selectAllPhotosAction {
    IMYGridGroup *group = self.sectionGroup;
    [self selectGroup:group];
    if (group.selected) {
        [self.topRightSelectButton setTitle:@"取消" forState:UIControlStateNormal];
    } else {
        [self.topRightSelectButton setTitle:@"全选" forState:UIControlStateNormal];
    }
}

// 将一个普通view转换成blurView(毛玻璃效果)
- (void)becomeToBlurView:(UIView *)blurView {
    UIToolbar *toolbar = [[UIToolbar alloc] initWithFrame:[blurView bounds]];
    [toolbar setTranslatesAutoresizingMaskIntoConstraints:NO];
    [blurView insertSubview:toolbar atIndex:0];

    [blurView addConstraints:[NSLayoutConstraint constraintsWithVisualFormat:@"H:|[toolbar]|"
                                                                     options:0
                                                                     metrics:0
                                                                       views:NSDictionaryOfVariableBindings(toolbar)]];
    [blurView addConstraints:[NSLayoutConstraint constraintsWithVisualFormat:@"V:|[toolbar]|"
                                                                     options:0
                                                                     metrics:0
                                                                       views:NSDictionaryOfVariableBindings(toolbar)]];

    //    [toolbar setBarTintColor:[UIColor yellowColor]];
}


- (void)reloadAlbum {
    [self.assets removeAllObjects];
    //切换相册不清空选中状态
    //    if ([self.dataTarget respondsToSelector:@selector(removeAllAsset)]) {
    //        [self.dataTarget removeAllAsset];
    //    }
    self.captionView.state = IMYCaptionViewStateLoading;

    if (!self.album) {
        if ([IMYAssetsManager authorizationStatus] == IMYAssetAuthorizationStatusLimited) {
            self.captionView.state = IMYCaptionViewStateHidden;
            [self reloadData];
        } else if (self.markerType == IMYGridCellMarkerTypeVideo) {
            self.captionView.state = IMYCaptionViewStateHidden;
            self.topQPCameraView.hidden = NO;
        } else {
            self.captionView.state = IMYCaptionViewStateNoResult;
        }
    } else {
        @weakify(self);
//        [self.album fetchAllAssetsWithFilter:self.markerType == IMYGridCellMarkerTypeVideo ? IMYAssetFilterVideo : IMYAssetFilterImage
//                                  completion:^(NSArray *assets) {
//            @strongify(self);
//            [self.assets addObjectsFromArray:assets];
//            [self sortAssets];
//            [self reloadData];
//            if (self.markerType == IMYGridCellMarkerTypeVideo) {
//                self.topQPCameraView.hidden = NO;
//            }
//            if ([assets count] || self.operationSize > 0) {
//                self.captionView.state = IMYCaptionViewStateHidden;
//            } else {
//                if (self.markerType == IMYGridCellMarkerTypeVideo) {
//                    self.captionView.state = IMYCaptionViewStateHidden;
//                } else {
//                    self.captionView.state = IMYCaptionViewStateNoResult;
//                }
//            }
//        }];
        
        BOOL filterImage = self.markerType == IMYGridCellMarkerTypeVideo ? NO : YES;
        [self.album enumerateAssetsWithOptions:(IMYAssetsSortTypeReverse)
                                    usingBlock:^(IMYAssetModel * _Nonnull asset) {
            @strongify(self);
            if (filterImage) {
                // add image asset
                if (asset.assetType == IMYAssetTypeImage) {
                    [self.assets addObject:asset];
                }
            } else {
                // add video asset
                if (asset.assetType == IMYAssetTypeVideo) {
                    [self.assets addObject:asset];
                }
            }
        }];
        
        [self sortAssets];
        [self reloadData];
        if (self.markerType == IMYGridCellMarkerTypeVideo) {
            self.topQPCameraView.hidden = NO;
        }
        if ([self.assets count] || self.operationSize > 0) {
            self.captionView.state = IMYCaptionViewStateHidden;
        } else {
            if (self.markerType == IMYGridCellMarkerTypeVideo) {
                self.captionView.state = IMYCaptionViewStateHidden;
            } else {
                self.captionView.state = IMYCaptionViewStateNoResult;
            }
        }
    }
}

- (void)sortAssets {
    self.assets = [NSMutableArray arrayWithArray:[self.assets sortedArrayUsingDescriptors:@[[[NSSortDescriptor alloc] initWithKey:@"creationDate" ascending:NO]]]];
    self.sortedGroups = [NSMutableArray arrayWithCapacity:0];
    if ([self.dataTarget respondsToSelector:@selector(sortAssets:)]) {
        self.sortedGroups = [self.dataTarget sortAssets:self.assets];
    } else {
        for (IMYAssetModel *asset in self.assets) {
            if (asset.fetchPhAsset.duration >= 601.0) {
                continue;
            }
            IMYGridGroup *selectGroup = self.sortedGroups.lastObject;
            if (selectGroup && [asset.creationDate isEqualToDateIgnoringTime:selectGroup.date]) {
                [selectGroup.assets addObject:asset];
            } else {
                IMYGridGroup *group = [[IMYGridGroup alloc] init];
                group.date = asset.creationDate;
                group.assets = [NSMutableArray array];
                [group.assets addObject:asset];
                [self.sortedGroups addObject:group];
            }
        }
    }
}

#pragma mark - Public

- (void)setAlbum:(IMYAssetsGroupModel *)album {
    if (_album != album || (album == nil && _album == nil)) {
        _album = album;
        [self reloadAlbum];
    }
}

- (void)reloadData {
    if ([IMYAssetsManager authorizationStatus] == IMYAssetAuthorizationStatusLimited) {
        self.operationSize = 1;
    } else {
        self.operationSize = 0;
    }
    [self.collectionView reloadData];
}

- (BOOL)scrollToDate:(NSDate *)date animated:(BOOL)animated {
    NSInteger section = 0;
    NSTimeInterval lastDateTimeInterval = 0;
    for (IMYGridGroup *group in self.sortedGroups) {
        if ([group.date isEqualToDateIgnoringTime:date]) {
            break;
        }
        //匹配最近的
        if (lastDateTimeInterval && [group.date isEarlierThanDate:date]) {
            NSTimeInterval matchDateTimeInterval = [date timeIntervalSinceDate:group.date];
            if (lastDateTimeInterval > matchDateTimeInterval) {
                break;
            } else {
                --section;
                break;
            }
        } else {
            lastDateTimeInterval = [group.date timeIntervalSinceDate:date];
        }
        ++section;
    }
    if (section == [self.sortedGroups count]) {
        --section;
    }
    [self.collectionView scrollToItemAtIndexPath:[NSIndexPath indexPathForRow:0 inSection:section] atScrollPosition:UICollectionViewScrollPositionTop animated:animated];
    //fix header高度
    [self.collectionView setContentOffset:CGPointMake(self.collectionView.contentOffset.x, self.collectionView.contentOffset.y - 34) animated:NO];
    return YES;
}

#pragma mark - UICollectionView

- (NSInteger)numberOfSectionsInCollectionView:(UICollectionView *)collectionView {
    return self.sortedGroups.count + self.operationSize;
}

- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    if (section == self.operationSize - 1) {
        // 只有添加更多图片
        return 1;
    } else {
        NSInteger realSection = section - self.operationSize;
        IMYGridGroup *group = self.sortedGroups[realSection];
        return group.assets.count;
    }
}

- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    if (indexPath.section == self.operationSize - 1) {
        // 只有添加更多图片
        UICollectionViewCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:@"TaskCell" forIndexPath:indexPath];
        [cell.contentView imy_removeAllSubviews];
        cell.contentView.backgroundColor = [UIColor imy_colorWithHexString:@"4D4D4D"];
        UIImageView *imageView = [[UIImageView alloc] init];
        [imageView imy_setImageForKey:@"pb_ic_take_add"];
        [cell.contentView addSubview:imageView];
        [imageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.height.width.mas_equalTo(@36);
            make.centerX.equalTo(cell.contentView);
            make.centerY.equalTo(cell.contentView).offset(-6);
        }];
        UILabel *label = [[UILabel alloc] init];
        label.textColor = [UIColor imy_colorForKey:kCK_White_A];
        label.font = [UIFont systemFontOfSize:10];
        label.text = @"添加更多照片";
        [cell.contentView addSubview:label];
        [label mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(imageView.mas_bottom).offset(1);
            make.centerX.equalTo(cell.contentView);
        }];
        return cell;
    }
    
    NSInteger realSection = indexPath.section - self.operationSize;
    IMYGridCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:@"IMYGridCell" forIndexPath:indexPath];
    [cell setMarkerType:self.markerType];
    IMYGridGroup *group = self.sortedGroups[realSection];
    IMYAssetModel *asset = group.assets[indexPath.row];
    cell.asset = asset;
    if ([self.dataTarget respondsToSelector:@selector(containsAsset:)] && (self.markerType != IMYGridCellMarkerTypeVideo)) {
        BOOL uploaded = [self getUploadedForAssetURL:asset.identifier];
        if (self.markerType == IMYGridCellMarkerTypeXiangCe) {
            uploaded = NO;
        }
        if ([self.dataTarget containsAsset:asset] && !uploaded) {
            cell.marked = YES;
        } else {
            [self deselectAsset:asset];
            cell.marked = NO;
        }
    }
    return cell;
}

- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath {
    if (indexPath.section == self.operationSize - 1) {
        imy_asyncMainBlock(^{
            [IMYAssetsManager presentLimitedLibraryPickerFromViewController:self];
        });
        return;
    }
    NSInteger realSection = indexPath.section - self.operationSize;
    IMYGridCell *cell = (IMYGridCell *)[collectionView cellForItemAtIndexPath:indexPath];
    IMYGridGroup *group = self.sortedGroups[realSection];
    IMYAssetModel *asset = group.assets[indexPath.row];
    BOOL uploaded = [self getUploadedForAssetURL:asset.identifier];
    if (self.markerType == IMYGridCellMarkerTypeXiangCe) {
        uploaded = NO;
    }
    if (self.markerType == IMYGridCellMarkerTypeVideo) {
        [IMYEventHelper event:@"spxz-sp"];
        double duration = asset.fetchPhAsset.duration;
        //        if (uploaded) {
        //            [UIWindow imy_showTextHUD:IMYString(@"视频已上传过哦~")];
        //            return;
        //        }
        //选择1秒以上的视频
        if (duration < 1) {
            [UIWindow imy_showTextHUD:IMYString(@"请选择大于1秒的视频哦~")];
        } else {
            //选择该视频，跳转预览页面
            if ([self.dataTarget respondsToSelector:@selector(previewAlbumVideo:)]) {
                [self.dataTarget previewAlbumVideo:asset];
            }
        }
    } else {
        if (uploaded) {
            [self deselectAsset:asset];
        } else {
            if (cell.marked) {
                [self deselectAsset:asset];
                cell.marked = !cell.marked;
                [cell showAnimation];
            } else {
                BOOL selecteEnabled = [self selectAsset:asset];
                if (selecteEnabled) {
                    cell.marked = !cell.marked;
                    [cell showAnimation];
                }
            }
        }
        [self checkState:group indexPath:indexPath];
    }
}

- (UICollectionReusableView *)collectionView:(UICollectionView *)collectionView viewForSupplementaryElementOfKind:(NSString *)kind atIndexPath:(NSIndexPath *)indexPath {
    
    if (indexPath.section == self.operationSize - 1) {
        // 只有添加更多图片
        return [collectionView dequeueReusableSupplementaryViewOfKind:kind withReuseIdentifier:@"TaskHeader" forIndexPath:indexPath];
    }
    
    UICollectionReusableView *reusableView = nil;
    NSInteger realSection = indexPath.section - self.operationSize;
    IMYGridGroup *group = self.sortedGroups[realSection];

    if (kind == UICollectionElementKindSectionHeader) {
        IMYGridHeader *header = [collectionView dequeueReusableSupplementaryViewOfKind:kind withReuseIdentifier:@"IMYGridHeader" forIndexPath:indexPath];
        self.headerDict[@(realSection)] = header;
        header.actionButton.hidden = self.hiddenAllSelectItem;
        if (!self.hiddenAllSelectItem) {
            header.actionButton.hidden = ![self canShowSelectItem:group];
        }
        @weakify(self);
        [header setTapAction:^(UIButton *sender) {
            @strongify(self);
            [self selectGroup:group];
        }];

        id<IMYPickUIProtocol> delegate = [IMYPickConfig defaultConfig].pickUIDelegate;
        if ([delegate respondsToSelector:@selector(groupTitleForDate:)]) {
            header.titleLabel.text = [delegate groupTitleForDate:group.date];
        } else if ([delegate respondsToSelector:@selector(groupAttributedTitleForDate:)]) {
            NSAttributedString *title = [delegate groupAttributedTitleForDate:group.date];
            header.titleLabel.attributedText = title;
        } else {
            header.titleLabel.text = nil;
        }
        //        if (group.date.year == [NSDate date].year) {
        //            header.titleLabel.text = [NSString stringWithFormat:IMYString(@"%ld月%ld日"), group.date.month, group.date.day];
        //        } else {
        //            header.titleLabel.text = [NSString stringWithFormat:IMYString(@"%ld年%ld月%ld日"), group.date.year, group.date.month, group.date.day];
        //        }
        //
        //        header.titleLabel.text = [NSString stringWithFormat:IMYString(@"出生%@ %@"), [[group.date imy_getDateZeroTime] babybirthdayString], header.titleLabel.text];

        if (group.selected) {
            [header.actionButton setTitle:@"取消" forState:UIControlStateNormal];
        } else {
            [header.actionButton setTitle:@"全选" forState:UIControlStateNormal];
        }
        header.actionButton.hidden = self.markerType == IMYGridCellMarkerTypeVideo;
        reusableView = header;
    }

    return reusableView;
}

#pragma mark - UIScrollViewDelegate
- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    CGFloat offsetY = scrollView.contentOffset.y;
    if (offsetY > 0) {
        self.topBlurView.hidden = NO;
    } else {
        self.topBlurView.hidden = YES;
        return;
    }


    // 计算当前offsetY位置,blurView对应的section.
    for (int i = 0; i < self.sectionArrayAxisY.count; i++) {
        CGFloat currentMaxY = [self.sectionArrayAxisY[i] floatValue];
        if (offsetY < currentMaxY) {
            if (currentMaxY - offsetY < self.topBlurView.imy_height) {
                self.topBlurView.imy_bottom = currentMaxY - offsetY;
            } else {
                self.topBlurView.imy_top = 0;
            }
            self.sectionGroup = self.sortedGroups[i];

            id<IMYPickUIProtocol> delegate = [IMYPickConfig defaultConfig].pickUIDelegate;
            if ([delegate respondsToSelector:@selector(groupTitleForDate:)]) {
                self.topLeftDateLabel.text = [delegate groupTitleForDate:self.sectionGroup.date];
            } else if ([delegate respondsToSelector:@selector(groupAttributedTitleForDate:)]) {
                self.topLeftDateLabel.attributedText = [delegate groupAttributedTitleForDate:self.sectionGroup.date];
            }

            if (self.sectionGroup.selected) {
                [self.topRightSelectButton setTitle:@"取消" forState:UIControlStateNormal];
            } else {
                [self.topRightSelectButton setTitle:@"全选" forState:UIControlStateNormal];
            }

            break;
        }
    }
}


#pragma mark - Private

- (BOOL)selectAsset:(IMYAssetModel *)asset {
    if ([self.dataTarget respondsToSelector:@selector(addAsset:)]) {
        return [self.dataTarget addAsset:asset];
    }
    return NO;
}

- (void)deselectAsset:(IMYAssetModel *)asset {
    if ([self.dataTarget respondsToSelector:@selector(removeAsset:)]) {
        [self.dataTarget removeAsset:asset];
    }
}

- (BOOL)getUploadedForAssetURL:(NSURL *)url {
    id<IMYPickUploadProtocol> delegate = [IMYPickConfig defaultConfig].uploadDelegate;
    BOOL uploaded = NO;
    if ([delegate respondsToSelector:@selector(getUploadStatusForAssetURL:)]) {
        IMYPickUploadStatus status = [delegate getUploadStatusForAssetURL:url];
        if (status == IMYPickUploadStatusNo) {
            uploaded = NO;
        } else {
            uploaded = YES;
        }
    }
    return uploaded;
}

// 先计算出每个模块的Y坐标范围. (只需要计算一次)
- (NSMutableArray<NSNumber *> *)sectionArrayAxisY {
    if (!_sectionArrayAxisY) {
        UICollectionViewFlowLayout *layout = (UICollectionViewFlowLayout *)self.collectionView.collectionViewLayout;

        // 每张照片之间的间距
        CGFloat itemSpacing = layout.minimumLineSpacing;
        // 头部的高度
        CGFloat headerHeight = layout.headerReferenceSize.height;
        // 尾部的高度
        CGFloat footerHeight = layout.footerReferenceSize.height;
        // 每张照片的高度
        CGFloat itemHeight = layout.itemSize.height;

        self.sectionArrayAxisY = [NSMutableArray new];
        NSUInteger itemInRow = 3;
        if (self.dataSource && [self.dataSource respondsToSelector:@selector(numberOfItemInRowInController:)]) {
            itemInRow = [self.dataSource numberOfItemInRowInController:self];
        }
        if (itemInRow == 0) {
            itemInRow = 3;
        }
        for (IMYGridGroup *group in self.sortedGroups) {
            // 所占行数,按照每行三张算
            int remainder = (group.assets.count % itemInRow) ? 1 : 0;
            int photoLineCount = (int)(group.assets.count / itemInRow) + remainder;
            CGFloat lastY = 0;
            if ([self.sectionArrayAxisY count] > 0) {
                lastY = [[self.sectionArrayAxisY lastObject] floatValue];
            }
            CGFloat Y = lastY + headerHeight + footerHeight + itemSpacing * (photoLineCount - 1) + itemHeight * (photoLineCount) + layout.sectionInset.top + layout.sectionInset.bottom;

            [self.sectionArrayAxisY addObject:[NSNumber numberWithFloat:Y]];
        }
    }
    return _sectionArrayAxisY;
}

/// 检查group里照片的选中状态，并改变对应的按钮状态
- (void)checkState:(IMYGridGroup *)group indexPath:(NSIndexPath *)indexPath {
    BOOL isAllSelected = [self isAllSelectedWithGroup:group];
    //dequeueReusableSupplementaryViewOfKind:withReuseIdentifier:forIndexPath:方法有坑，会重复创建headerView
    NSInteger realSection = indexPath.section - self.operationSize;
    IMYGridHeader *header = self.headerDict[@(realSection)];

    if (isAllSelected) {
        [header.actionButton setTitle:@"取消" forState:UIControlStateNormal];
        group.selected = YES;
        [self scrollViewDidScroll:self.collectionView];
    } else {
        [header.actionButton setTitle:@"全选" forState:UIControlStateNormal];
        group.selected = NO;
        [self scrollViewDidScroll:self.collectionView];
    }
}

/// group里的是否全部已选中
- (BOOL)isAllSelectedWithGroup:(IMYGridGroup *)group {
    NSInteger isAllSelected = NO;
    if ([self.dataTarget respondsToSelector:@selector(containsAsset:)]) {
        BOOL hasSelect = NO;
        BOOL hasUnselect = NO;
        for (IMYAssetModel *asset in group.assets) {
            BOOL uploaded = [self getUploadedForAssetURL:asset.identifier];
            if (uploaded) {
                //已上传的，不作判断，跳过
            } else if ([self.dataTarget containsAsset:asset] && !uploaded) {
                hasSelect = YES;
                if (hasUnselect) {
                    break;
                }
            } else {
                [self deselectAsset:asset];
                hasUnselect = YES;
                if (hasSelect) {
                    break;
                }
            }
        }
        if (hasSelect && !hasUnselect) {
            isAllSelected = YES;
        } else if (!hasSelect && hasUnselect) {
            isAllSelected = NO;
        } else {
            isAllSelected = NO;
        }
    }
    return isAllSelected;
}

/// 是否可以显示“全选”或“取消”
- (BOOL)canShowSelectItem:(IMYGridGroup *)group {
    BOOL canShow = NO;
    id<IMYPickUploadProtocol> delegate = [IMYPickConfig defaultConfig].uploadDelegate;
    if ([delegate respondsToSelector:@selector(getUploadStatusForAssetURL:)]) {
        for (IMYAssetModel *asset in group.assets) {
            IMYPickUploadStatus status = [delegate getUploadStatusForAssetURL:asset.identifier];
            if (status == IMYPickUploadStatusNo) {
                canShow = YES;
                break;
            }
        }
    }
    return canShow;
}

@end
