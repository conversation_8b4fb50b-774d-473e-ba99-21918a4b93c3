//
//  IMYGridCell.h
//  YuEr
//
//  Created by mario on 15/7/3.
//  Copyright (c) 2015年 meiyou. All rights reserved.
//

#import <UIKit/UIKit.h>

typedef NS_ENUM(NSUInteger, IMYGridCellMarkerType) {
    IMYGridCellMarkerTypeYuer,
    IMYGridCellMarkerTypeYunqi,
    IMYGridCellMarkerTypeXiangCe,
    IMYGridCellMarkerTypeVideo,
};

@class IMYAssetModel;

@interface IMYGridCell : UICollectionViewCell

@property (nonatomic, strong) IMYAssetModel *asset;

@property (nonatomic, assign, readonly) BOOL uploaded;

@property (nonatomic, assign) BOOL marked;

@property (weak, nonatomic) IBOutlet UIImageView *mediaView;
@property (weak, nonatomic) IBOutlet UIImageView *indicatorView;
@property (weak, nonatomic) IBOutlet UIView *hasUploadBG;
@property (weak, nonatomic) IBOutlet UIView *videoInfoBG;

@property (nonatomic, assign) IMYGridCellMarkerType markerType;

- (void)updateHasUploadLabel;

- (void)showAnimation;

@end
