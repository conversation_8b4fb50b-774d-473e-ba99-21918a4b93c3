//
//  IMYPickViewController.h
//  YuEr
//
//  Created by mario on 15/7/3.
//  Copyright (c) 2015年 meiyou. All rights reserved.
//

#import "IMYGridCell.h"
#import "IMYGridViewController.h"
#import "IMYPickConfig.h"
#import "IMYPickConstants.h"
#import <UIKit/UIKit.h>

FOUNDATION_EXPORT NSUInteger kIMYPickViewControllerMaxPickCount;

@class IMYPickViewController;

@protocol IMYPickViewControllerDelegate <NSObject>

@optional
- (NSInteger)maxCountInPickViewController:(IMYPickViewController *)pickController;

- (void)pickViewControllerDidUpdated:(IMYPickViewController *)pickController;

//育儿视频调用
- (void)qpCameraAction;

//育儿预览相册视频
- (void)previewAlbumVideo:(IMYAssetModel *)asset;

@end

@interface IMYPickViewController : UIViewController

@property (nonatomic, weak) id<IMYPickViewControllerDelegate> delegate;
@property (nonatomic, strong) NSDate *dateToScroll;
@property (nonatomic, assign) BOOL hiddenAllSelectItem;
@property (nonatomic, assign) IMYGridCellMarkerType markerType;

- (NSArray *)selectedAssets;

@end
