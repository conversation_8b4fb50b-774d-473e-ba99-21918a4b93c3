//
//  IMYVKURIRegister.m
//  IMYViewKit
//
//  Created by ljh on 15/7/15.
//  Copyright (c) 2015年 IMY. All rights reserved.
//

#import "IMYVKURIRegister.h"
#import "IMYCoolShareSheet.h"
#import "IMYH5FullScreenVC.h"
#import "IMYVKURIAudioRegister.h"
#import "IMYViewKit.h"
#import "IMYWebTemplateLoader.h"
#import "IMYVKLocalWebViewController.h"
#import "IMYAliyunOSSFileObject.h"
#import <CocoaSecurity/Base64.h>
#import <Photos/Photos.h>
#import <StoreKit/StoreKit.h>
#import <UserNotifications/UserNotifications.h>
#import "IMYVKPageSheetViewController.h"
#import <IOC-Protocols/IOCAppInfo.h>
#import <IOC-Protocols/IOCBabyInfo.h>

#import "IMYEventSourceManager.h"

@interface IMYURIAlbumOperator : NSObject
// 存储图片
+ (void)savePhotoWithURIAction:(IMYURIActionBlockObject *)actionObject;
// 查看大图
+ (void)galleryPhotoWithURIAction:(IMYURIActionBlockObject *)actionObject;
// 选择图片
+ (void)showPhotoSelectorWithURIAction:(IMYURIActionBlockObject *)actionObject;
// 上传图片
+ (void)ossUploadWithURIAction:(IMYURIActionBlockObject *)actionObject;
// 批量上传asset图片
+ (void)ossBatchAssetsUploadWithURIAction:(IMYURIActionBlockObject *)actionObject;

@end

/// 柚币刷新通知
NSString *const kIMYURIYoubiRefreshNotification = @"kIMYURIYoubiRefreshNotification";

@implementation IMYVKURIRegister

IMY_KYLIN_FUNC_PREMAIN_ASYNC {
    ///注册URI拦截
    [IMYVKURIRegister registerDefaultURIActions];
}

+ (instancetype)sharedInstace {
    static id instance;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [self new];
    });
    return instance;
}

+ (void)registerDefaultURIActions {
    [self registerMapAction];
    [self registerMobclickAction];
    [self registerNavgationAction];
    [self registerInfoAction];
    [self registerWebJumpAction];
    [self registerTopbarAction];
    [self registerViewConfig];
    [self registerImageURI];
    [self registerShareAction];
    [self registerUserAddress];
    [self registerKeyboardStatus];
    [self registerViewKitAction];
    [IMYVKURIAudioRegister registerAudioAction];
}

+ (void)saveURIUserDefault {
    NSDictionary *dict = [[self getURIUserDefault] copy];
    [NSObject imy_asyncBlock:^{
        NSString *defaultPath = [LKDBUtils getPathForDocuments:@"URIUserDefault" inDir:@"uriMap"];
        [dict writeToFile:defaultPath atomically:YES];
    } onLevel:IMYQueueLevelHighlight afterSecond:0.3 forKey:@"IMYSaveURIUserDefault"];
}

+ (NSMutableDictionary *)getURIUserDefault {
    static NSMutableDictionary *mutableDict = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        NSString *defaultPath = [LKDBUtils getPathForDocuments:@"URIUserDefault" inDir:@"uriMap"];
        mutableDict = [NSMutableDictionary dictionaryWithContentsOfFile:defaultPath];
        if (!mutableDict) {
            mutableDict = [NSMutableDictionary dictionary];
        }
    });
    return mutableDict;
}

#pragma mark - 分享相关
///是否显示了分享蒙版
static BOOL kHasShowShareSheetBox = NO;
+ (void)setHasShowShareSheetBox:(BOOL)isShow {
    kHasShowShareSheetBox = isShow;
}

+ (NSString *)fullURLWithWebURL:(NSString *)url baseURL:(NSURL *)baseURL {
    if (imy_isEmptyString(url)) {
        return @"";
    }
    if (!baseURL) {
        baseURL = [NSURL URLWithString:mycdn_seeyouyima_com];
    }
    if ([url hasPrefix:@"//"]) {
        return [NSString stringWithFormat:@"%@:%@", baseURL.scheme, url];
    } else if ([url hasPrefix:@"/"]) {
        return [NSString stringWithFormat:@"%@://%@%@", baseURL.scheme, baseURL.host, url];
    }
    if (![url containsString:@"://"]) {
        return [NSString stringWithFormat:@"%@://%@", baseURL.scheme, url];
    }
    return url;
}

+ (void)shareWithType:(IMYShareType)shareType actionObject:(IMYURIActionBlockObject *)actionObject {
    NSDictionary *shareParams = actionObject.uri.params;
    
    NSString *title = shareParams[@"title"];
    NSString *content = shareParams[@"content"];
    NSString *imageURL = shareParams[@"imageURL"];
    NSString *fromURL = shareParams[@"fromURL"];
    NSString *comment = shareParams[@"comment"];
    NSInteger moduleId = [shareParams[@"moduleId"] integerValue];
    
    NSMutableDictionary *ext = [NSMutableDictionary dictionary];
    [ext imy_setNonNilObject:shareParams[@"miniProgramUserName"] forKey:@"miniProgramUserName"];
    [ext imy_setNonNilObject:shareParams[@"miniProgramPath"] forKey:@"miniProgramPath"];
    [ext imy_setNonNilObject:shareParams[@"miniProgramType"] forKey:@"miniProgramType"];
    [ext imy_setNonNilObject:shareParams[@"mediaResources"] forKey:@"mediaResources"];
    [ext imy_setNonNilObject:shareParams[@"hashtags"] forKey:@"hashtags"];

    IMYShareContentType mediaType = [shareParams[@"mediaType"] integerValue];
    
    // 不是 复制链接 分享类型，才对 fromURL 做完整性修复
    if (IMYShareTypeCopy != shareType) {
        imageURL = [self fullURLWithWebURL:imageURL baseURL:actionObject.webView.URL];
        fromURL = [self fullURLWithWebURL:fromURL baseURL:actionObject.webView.URL];
    }
    if ([IMYMeetyouHTTPHooks isMeiYouSite:fromURL]) {
        NSString *platform = [IMYPublicShareManager getBIPlatformStringWithType:shareType];
        if (platform) {
            fromURL = [fromURL imy_appendingURLParams:@{@"public_type":platform}];
        }
    }
    
    NSInteger URIType = [IMYPublicShareManager shareURITypeFromIMYType:shareType];
    if ([shareParams[@"click_callback"] boolValue]) {
        // 有需要的才回调 点击type
        [actionObject callbackWithObject:@{@"type":@(URIType),@"click":@1}];
    }
    
    [IMYPublicShareManager new]
    .title(title)
    .content(content)
    .imageURL(imageURL)
    .image(nil)
    .fromURL(fromURL)
    .comment(comment)
    .shareType(shareType)
    .ext(ext)
    .contentMediaType(mediaType)
    .fromModule(moduleId)
    .callback(^(BOOL success) {
        NSDictionary *dict = @{ @"type": @(URIType),
                                @"success": success ? @1 : @0 };
        [actionObject callbackWithObject:dict];
        
        // 分享结果埋点
        NSString *clickShareSheetName = [IMYCoolShareSheet clickShareSheetName];
        NSString *shareRet = success ? @"分享成功" : @"分享失败";
        NSString *shareFromUrl = [IMYCoolShareSheet shareFromUrl];
        [IMYCoolShareSheet ptFxjgWithAction:2 publicType:clickShareSheetName publicInfo:shareRet url:shareFromUrl];
    })
    .share();
}

+ (void)shareWithSheetType:(IMYCoolShareSheetType)sheetType actionObject:(IMYURIActionBlockObject *)actionObject {
    if (sheetType == IMYCoolShareSheetTypeMeetyouZone) {
        NSString *momentURI = actionObject.uri.params[@"momentURI"]; // 我的状态URI
        IMYURI *uri = [IMYURI uriWithURIString:momentURI];
        [uri appendingParams:actionObject.uri.params];
        [uri appendingParams:@{@"type": @"1"}];
        [[IMYURIManager shareURIManager] runActionWithURI:uri completed:^(IMYURIActionBlockObject *actionObject) {
            NSDictionary *dict = @{ @"type": @(8),
                                    @"success": @0 };
            [actionObject callbackWithObject:dict];
        }];
    } else if (sheetType == IMYCoolShareSheetTypeSaveImage) {
        NSDictionary *dict = @{ @"type": @(14),
                                @"success": @0 };
        [actionObject callbackWithObject:dict];
    } else if (sheetType == IMYCoolShareSheetTypeCollect) {
        // 点击收藏
        [actionObject.webView sendEvent:@"tips/collectClick" params:nil];
    } else if (sheetType == IMYCoolShareSheetTypeReportError) {
        // 点击纠错
        NSString *feedbackContent = actionObject.uri.params[@"reportErrorTitle"];
        NSUInteger feebackId = [actionObject.uri.params[@"reportErrorTypeID"] integerValue];
        IMYURI *uri = [IMYURI uriWithURIString:@"feedback"];
        [uri appendingParams:@{ @"content": feedbackContent,
                                @"defaultSetId": @(feebackId) }];
        [[IMYURIManager shareURIManager] runActionWithURI:uri completed:nil];
        //点击纠错，回调对应的type
        NSDictionary *dict = @{ @"type": @(IMYCoolShareSheetTypeReportError),
                                @"success": @0 };
        [actionObject callbackWithObject:dict];
    } else if (sheetType == IMYCoolShareSheetTypeFeedback) {
        // 点击意见反馈
        [actionObject.webView sendEvent:@"tips/feedbackClick" params:nil];
    } else {
        NSDictionary *dict = @{ @"type": @0,
                                @"success": @0 };
        [actionObject callbackWithObject:dict];
    }
}
+ (void)shareDoWithActionObject:(IMYURIActionBlockObject *)actionObject {
    if (kHasShowShareSheetBox) {
        imy_asyncMainBlock(3, ^{
            kHasShowShareSheetBox = NO;
        });
        return;
    }
    kHasShowShareSheetBox = YES;
    NSDictionary * const shareParams = actionObject.uri.params;
    IMYShareUriType uriType = [shareParams[@"type"] integerValue]; // 直接打开某个平台的分享
    if (uriType > 0) { // 直接进入指定平台的分享跳转，当uriType==0，显示全部分享平台
        kHasShowShareSheetBox = NO;
        IMYShareType shareType = [IMYPublicShareManager shareIMYTypeFromURIType:uriType];
        [self shareWithType:shareType actionObject:actionObject];
    } else {
        NSArray *configList = nil;
        if ([shareParams[@"thirdPartyWeb"] boolValue]) {
            BOOL isShowThirdParty = YES;
            IMYCoolShareConfig *shareConfig = nil;
            if (isShowThirdParty) {
                shareConfig = [IMYCoolShareConfig baseShareConfig];
            } else {
                shareConfig = [[IMYCoolShareConfig alloc] init];
            }
            IMYCoolShareItem *refreshItem = [IMYCoolShareItem itemWithName:@"刷新" icon:@"web_icon_refresh" tag:IMYCoolShareSheetTypeOther];
            refreshItem.shareBlock = ^{
                [actionObject.webView reload];
            };
            IMYCoolShareItem *copyItem = [IMYCoolShareItem itemWithName:@"复制链接" icon:@"web_icon_copy" tag:IMYCoolShareSheetTypeOther];
            copyItem.shareBlock = ^{
                NSString *url = shareParams[@"fromURL"];
                if (imy_isNotBlankString(url)) {
                    UIPasteboard *pasteboard = [UIPasteboard generalPasteboard];
                    pasteboard.string = url;
                    [UIWindow imy_showTextHUD:@"已复制"];
                } else {
                    [UIWindow imy_showTextHUD:@"复制失败"];
                }
            };
            IMYCoolShareItem *reportItem = [IMYCoolShareItem itemWithName:@"举报" icon:@"web_icon_report" tag:IMYCoolShareSheetTypeOther];
            reportItem.shareBlock = ^{
                [IMYActionSheet sheetWithCancelTitle:IMYString(@"取消")
                                         otherTitles:@[@"网页包含欺诈类信息", @"网页包含症状敏感类信息", @"网页包含色情信息", @"网页在收集个人隐私信息", @"网页包含暴力恐怖信息"]
                                              action:^(NSInteger index) {
                    [[IMYPublicServerRequest postPath:@"third_web_report"
                                           host:data_seeyouyima_com
                                         params:@{ @"reason_id": @(index),
                                                   @"url": shareParams[@"fromURL"] }
                                        headers:nil] subscribeNext:^(id<IMYHTTPResponse> response) {
                        id res = [response responseObject];
                        NSInteger code = [res[@"code"] integerValue];
                        if (code != 0) {
                            [UIWindow imy_showTextHUD:@"举报失败"];
                        } else {
                            [UIWindow imy_showTextHUD:@"收到举报，我们将尽快核实处理"];
                        }
                    }];
                }];
            };
            if (isShowThirdParty) {
                [shareConfig addShareItemsAtSecondShareItemsGroup:@[refreshItem, copyItem, reportItem]];
            } else {
                [shareConfig addShareItemsAtFirstShareItemsGroup:@[refreshItem, copyItem, reportItem]];
            }
            configList = @[shareConfig.firstShareItemsGroup, shareConfig.secondShareItemsGroup];
        } else {
            IMYCoolShareConfig *shareConfig = [IMYCoolShareConfig webConfig];
            shareConfig.showMeetyouZone = [shareParams[@"moment"] boolValue]; // 显示“我的动态”
            shareConfig.showCopyLink = [(shareParams[@"copylink"] ?: @YES)boolValue]; // 显示“复制链接”
            shareConfig.showDingTalk = [shareParams[@"showDingTalk"] boolValue];  //钉钉
            shareConfig.showDouYin = [shareParams[@"showDouYin"] boolValue] && [IMYShareSDK isShareTypeAppInstalled:IMYShareSDKTypeDouYin]; // 抖音
            shareConfig.showXiaoHongShu = [shareParams[@"showXiaoHongShu"] boolValue] && [IMYShareSDK isShareTypeAppInstalled:IMYShareSDKTypeXiaoHongShu]; // 小红书
            
            if ([shareParams[@"hideWeixin"] boolValue]) {
                shareConfig.showWechat = NO;
            }
            if ([shareParams[@"hideWeixinTimeline"] boolValue]) {
                shareConfig.showWechatTimeline = NO;
            }
            if ([shareParams[@"hideQQ"] boolValue]) {
                shareConfig.showQQ = NO;
            }
            if ([shareParams[@"hideQZone"] boolValue]) {
                shareConfig.showQZone = NO;
            }
            if ([shareParams[@"hideWeibo"] boolValue]) {
                shareConfig.showWeibo = NO;
            }
            
            // 显示“收藏”
            if ([shareParams[@"collectTip"] boolValue]) {
                shareConfig.showCollection = YES;
                if ([shareParams[@"collectTipStatus"] boolValue]) {
                    shareConfig.isCollected = YES;
                }
            }
            // 显示“纠错”
            if ([shareParams[@"reportError"] boolValue]) {
                shareConfig.showReportError = YES;
                shareConfig.reportErrorTitle = shareParams[@"reportErrorTitle"];
                shareConfig.reportErrorName = shareParams[@"reportErrorName"];
            }
            // 显示保存按钮
            if ([shareParams[@"showSaveButton"] boolValue] && [shareParams[@"showImage"] boolValue] && imy_isNotBlankString(shareParams[@"imageURL"])) {
                shareConfig.showSaveButton = YES;
            }
            // 显示“意见反馈”（知识详情页新增弹窗页）
            if ([shareParams[@"showFeedback"] boolValue]) {
                shareConfig.showFeedback = YES;
            }
            // 获得分享配置项
            configList = [shareConfig twoLineConfigLists];
            
            // 宝宝记修改
            if (IMYPublicAppHelper.isYunqi) {
                for (IMYCoolShareItem *item in [configList lastObject]) {
                    if (item.tag == IMYCoolShareSheetTypeCollect) {
                        if (shareConfig.isCollected) {
                            item.name = @"已收藏";
                            item.normalIcon = @"BBJ_all_share_btn_favorited_hover";
                        } else {
                            item.name = @"收藏";
                            item.normalIcon = @"BBJ_all_share_btn_favorited";
                        }
                    }
                }
            }
        }
        
        UIViewController *showVC = actionObject.getUsingViewController.imy_navigationController;
        
        if ([shareParams[@"showImage"] boolValue]) {
            //使用显示图片的分享样式
            UIScrollView *shareScrollView = [[UIScrollView alloc] init];
            UIImageView *realShowImageView = [[UIImageView alloc] init];
            {
                shareScrollView.clipsToBounds = NO;
                shareScrollView.tag = 101;
                shareScrollView.showsVerticalScrollIndicator = NO;
                shareScrollView.showsHorizontalScrollIndicator = NO;
                shareScrollView.frame = CGRectMake(0, 0, SCREEN_WIDTH - IMYIntegerBy375Design(40) * 2, SCREEN_HEIGHT);
                
                realShowImageView.frame = shareScrollView.bounds;
                realShowImageView.contentMode = UIViewContentModeScaleAspectFit;
                [realShowImageView imy_drawAllCornerRadius:12];
                [shareScrollView addSubview:realShowImageView];
                
                NSString *imageURL = shareParams[@"imageURL"];
                @weakify(realShowImageView, shareScrollView);
                [realShowImageView sd_setImageWithURL:[NSURL imy_URLWithString:imageURL]
                                     placeholderImage:nil
                                              options:SDWebImageAvoidAutoSetImage
                                            completed:^(UIImage *image, NSError *error, SDImageCacheType cacheType, NSURL *imageURL) {
                    @strongify(realShowImageView, shareScrollView);
                    // 加延迟等待内部修正frame
                    [self shareDoWithImage:image refitView:realShowImageView scrollBox:shareScrollView];
                }];
            }
            
            // 显示分享界面
            @weakify(self);
            [IMYCoolShareSheet customShareInViewController:actionObject.getUsingViewController.imy_navigationController customView:shareScrollView configList:configList indexBlock:^(IMYCoolShareSheetType const itemType, IMYShareType const shareType) {
                @strongify(self);
                kHasShowShareSheetBox = NO;
                /// -1: 未定义的分享类型
                if (shareType == -1) {
                    if (itemType == IMYCoolShareSheetTypeMeetyouZone || itemType == IMYCoolShareSheetTypeSaveImage) {
                        //图片保存进手机相册
                        if (realShowImageView.image) {
                            [UIImage imy_saveToPhotosWithImage:realShowImageView.image
                                                complatedBlock:^(NSError *error) {
                                if (!error) {
                                    if (itemType == IMYCoolShareSheetTypeMeetyouZone) {
                                        [UIWindow imy_showTextHUD:@"图片保存成功，可发布分享"];
                                    } else {
                                        [UIWindow imy_showTextHUD:@"保存成功"];
                                    }
                                } else if (error.code != -1) {
                                    [UIWindow imy_showTextHUD:@"图片保存失败，请稍后再试"];
                                }
                            }];
                        } else {
                            [UIWindow imy_showTextHUD:@"图片加载缓慢，请稍后再试"];
                        }
                    }
                    [self shareWithSheetType:itemType actionObject:actionObject];
                } else {
                    [self shareWithType:shareType actionObject:actionObject];
                }
            }];
        } else {
            // 弹出多平台选择界面
            [IMYCoolShareSheet customShareInViewController:showVC
                                                configList:configList
                                                indexBlock:^(IMYCoolShareSheetType itemType, NSInteger shareType) {
                kHasShowShareSheetBox = NO;
                /// -1: 未定义的分享类型
                if (shareType == -1) {
                    [self shareWithSheetType:itemType actionObject:actionObject];
                } else {
                    [self shareWithType:shareType actionObject:actionObject];
                }
            }];
        }
    }
}

+ (void)shareDoWithImage:(UIImage *)image refitView:(UIImageView *)imageView scrollBox:(UIScrollView *)scrollView {
    if (!scrollView) {
        return;
    }
    if (scrollView.imy_height < SCREEN_HEIGHT - 1) {
        [UIView performWithoutAnimation:^{
            imageView.frame = scrollView.bounds;
            if (image.size.width > 0 && image.size.height > 0) {
                double imageAspect = image.size.height / image.size.width;
                double boxAspect = imageView.imy_height / imageView.imy_width;
                if (imageAspect > boxAspect) {
                    imageView.imy_height = imageView.imy_width * imageAspect;
                    scrollView.contentSize = CGSizeMake(0, imageView.imy_height);
                }
            }
        }];
        [UIView transitionWithView:scrollView
                          duration:0.22
                           options:UIViewAnimationOptionTransitionCrossDissolve
                        animations:^{
            imageView.image = image;
        } completion:nil];
    } else {
        @weakify(imageView, scrollView);
        imy_asyncMainBlock(0.1, ^{
            @strongify(imageView, scrollView);
            [self shareDoWithImage:image refitView:imageView scrollBox:scrollView];
        });
    }
}

+ (void)registerShareAction {
    [[IMYURIManager shareURIManager] addForPath:@"share/do"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        [self shareDoWithActionObject:actionObject];
    }];
    
    [[IMYURIManager shareURIManager] addForPath:@"goRating/alert" withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        [SKStoreReviewController requestReview];
    }];
    
    ///开启分享按钮
    [[IMYURIManager shareURIManager] addForPath:@"share/show/topRightButton"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        id webVC = actionObject.getUsingViewController;
        if ([webVC respondsToSelector:@selector(setEnableShareButtonAction:)] && [webVC respondsToSelector:@selector(setShowShareButton:)]) {
            [webVC setEnableShareButtonAction:NO];
            [webVC setShowShareButton:YES];
        }
    }];
    
    ///隐藏分享按钮
    [[IMYURIManager shareURIManager] addForPath:@"share/hidden/topRightButton"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        id webVC = actionObject.getUsingViewController;
        if ([webVC respondsToSelector:@selector(setEnableShareButtonAction:)] && [webVC respondsToSelector:@selector(setShowShareButton:)]) {
            [webVC setEnableShareButtonAction:NO];
            [webVC setShowShareButton:NO];
        }
    }];
    
    [[IMYURIManager shareURIManager] addForPath:@"qrcode/create"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSString *text = actionObject.uri.params[@"text"];
        NSInteger width = [actionObject.uri.params[@"width"] integerValue];
        NSInteger height = [actionObject.uri.params[@"height"] integerValue];
        if (width <= 0) {
            width = 300;
        }
        if (height <= 0) {
            height = 300;
        }
        void (^callbackBlock)(NSString *, NSString *) = ^(NSString *url, NSString *error) {
            imy_asyncMainExecuteBlock(^{
                NSMutableDictionary *dict = [NSMutableDictionary dictionary];
                dict[@"url"] = url;
                dict[@"error"] = error;
                [actionObject callbackWithObject:dict];
            });
        };
        [NSObject imy_asyncBlock:^{
            UIImage *qrImage = [UIImage imy_qrcodeImageWithText:text size:CGSizeMake(width, height)];
            if (!qrImage) {
                callbackBlock(nil, @"create qrimage fails!");
                return;
            }
            NSData *imageData = UIImageJPEGRepresentation(qrImage, 0.95);
            NSString *filename = [NSString stringWithFormat:@"qrcode/%@_%ld_%ld_%ld.jpg", [IMYPublicAppHelper shareAppHelper].userid, (long)CFAbsoluteTimeGetCurrent(), width, height];
            id<IMYOSSFileObject> fileObject = [[IMYOSS defaultUploader] fileObjectWithName:filename data:imageData];
            [[IMYOSS defaultUploader] uploadObject:fileObject
                                     progressBlock:nil
                                    complatedBlock:^(id<IMYOSSFileObject> _Nonnull object, NSError *_Nullable error) {
                callbackBlock(object.url.absoluteString, error.localizedDescription);
            }];
        }];
    }];
    
    //小程序
    [[IMYURIManager shareURIManager] addForPath:@"weixin/invokeMiniprogram"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        if (![IMYShareSDK isShareTypeAppInstalled:IMYShareSDKTypeWeChatSession]) {
            // 未安装微信，获取 降级URI 进行跳转
            NSString *downgrade_uri = actionObject.uri.params[@"downgrade_uri"];
            if (imy_isNotBlankString(downgrade_uri)) {
                [[IMYURIManager shareURIManager] runActionWithString:downgrade_uri];
            } else {
                // 没有降级策略，在进行弹窗提示
                UIViewController *current = [UIViewController imy_currentTopViewController];
                [current imy_showTextHUD:@"您尚未安装微信哦~"];
            }
            return;
        }
        NSMutableDictionary *ext = [NSMutableDictionary dictionary];
        [ext imy_setNonNilObject:actionObject.uri.params[@"user_name"] forKey:@"miniProgramUserName"];
        [ext imy_setNonNilObject:actionObject.uri.params[@"path"] forKey:@"miniProgramPath"];
        [ext imy_setNonNilObject:actionObject.uri.params[@"type"] forKey:@"miniProgramType"];
        id<IMYIContent> content = [IMYShareSDK contentWithText:nil image:nil title:nil url:nil description:nil ext:ext contentType:IMYIContentTypeGotoWXMiniProgram];
        [IMYShareSDK shareContent:content
                             type:IMYShareSDKTypeWeChatSession
                       completion:^(BOOL result, NSError *error){
            
        }];
    }];
    
    //微信业务请求（广告等）
    [[IMYURIManager shareURIManager] addForPath:@"weixin/invokeBusinessView"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        if (![IMYShareSDK isShareTypeAppInstalled:IMYShareSDKTypeWeChatSession]) {
            // 未安装微信，获取 降级URI 进行跳转
            NSString *downgrade_uri = actionObject.uri.params[@"downgrade_uri"];
            if (imy_isNotBlankString(downgrade_uri)) {
                [[IMYURIManager shareURIManager] runActionWithString:downgrade_uri];
            } else {
                // 没有降级策略，在进行弹窗提示
                UIViewController *current = [UIViewController imy_currentTopViewController];
                [current imy_showTextHUD:@"您尚未安装微信哦~"];
            }
            // 回调结果, 0：未安装微信 1: 成功， 2：失败
            [actionObject callbackWithObject:@{
                @"status": @0,
            }];
            return;
        }
#if __has_include(<IMYDynamicFrameworks/WXApi.h>)
        NSString *businessType = actionObject.uri.params[@"businessType"];
        if (imy_isEmptyString(businessType)) {
            // 回调结果, 0：未安装微信 1: 成功， 2：失败
            [actionObject callbackWithObject:@{
                @"status": @2,
            }];
            return;
        }
        WXOpenBusinessViewReq *req = [WXOpenBusinessViewReq object];
        req.businessType = businessType;
        req.extInfo = actionObject.uri.params[@"extInfo"];
        req.query = actionObject.uri.params[@"query"];
        [WXApi sendReq:req completion:^(BOOL success) {
            // 回调结果, 0：未安装微信 1: 成功， 2：失败
            [actionObject callbackWithObject:@{
                @"status": @(success ? 1 : 2),
            }];
        }];
#endif
    }];

}

#pragma mark - 点击

+ (void)registerMobclickAction {
    ///写入友盟事件
    [[IMYURIManager shareURIManager] addForPath:@"mobclick/*"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSString *event = [[actionObject.uri.path componentsSeparatedByString:@"/"] lastObject];
        NSInteger addType = [actionObject.uri.info[@"type"] integerValue];
        [IMYEventHelper event:event addType:addType attributes:actionObject.uri.params];
    }];
    
    [[IMYURIManager shareURIManager] addForPath:@"mobclick"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSString *event = [actionObject.uri.params objectForKey:@"event"];
        NSInteger addType = [actionObject.uri.params[@"type"] integerValue];
        NSDictionary *attributes = actionObject.uri.params[@"attributes"];
        [IMYEventHelper event:event addType:addType attributes:attributes];
    }];
}

#pragma mark - 数据读取：网络请求、存取

+ (NSDictionary *)resolverCode:(NSInteger)code data:(NSDictionary *)data message:(NSString *)message {
    
    NSNumber *mCode = data[@"code"];
    id mData = data[@"data"];
    NSString *mMessage = data[@"message"];
    
    BOOL hasValid = (mCode && mData && mMessage && data.count == 3);
    if (!hasValid) {
        mCode = @(code);
        mData = data ?: @{};
        mMessage = message ?: @"";
    }
    return @{@"code": mCode,
             @"data": mData,
             @"message": mMessage};
}

+ (void)registerMapAction {
    ///URI key value 存取方法
    [[IMYURIManager shareURIManager] addForPath:@"map/get"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSMutableDictionary *map = [NSMutableDictionary dictionary];
        NSDictionary *userdefault = [self getURIUserDefault];
        NSArray *allKeys = actionObject.uri.params[@"keys"];
        for (NSString *key in allKeys) {
            id obj = userdefault[key];
            if (obj) {
                map[key] = obj;
            }
        }
        [actionObject callbackWithObject:map];
    }];
    
    [[IMYURIManager shareURIManager] addForPath:@"map/set"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSDictionary *map = actionObject.uri.params;
        [[self getURIUserDefault] addEntriesFromDictionary:map];
        [self saveURIUserDefault];
    }];
    
    [[IMYURIManager shareURIManager] addForPath:@"request"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSDictionary *requestMap = actionObject.uri.params;
        NSString *url = requestMap[@"url"];
        NSString *method = requestMap[@"method"];
        NSDictionary *headers = requestMap[@"headers"];
        NSDictionary *params = requestMap[@"params"];
        const BOOL nowrap = [requestMap[@"nowrap"] boolValue];
        
        IMYHTTPSerializerType requestSerializer = IMYHTTPSerializerTypeJSON;
        NSString *contentType = headers[@"Content-Type"];
        if ([contentType containsString:@"application/x-www-form-urlencoded"]) {
            requestSerializer = IMYHTTPSerializerTypeData;
        }
        
        HTTPMethod httpMethod = IMYHTTPMethodFromString(method);
        id<IMYHTTPBuildable> buildable = [IMYServerRequest buildable];
        buildable.Path(url).Method(httpMethod).Host(@"").Parameters(params).Headers(headers).RequestSerializerType(requestSerializer).ResponseSerializerType(IMYHTTPSerializerTypeData);
        [buildable.signal subscribeNext:^(id<IMYHTTPResponse> response) {
            NSData *data = response.responseData;
            id jsonObject = [data imy_jsonObject];
            if (!jsonObject && nowrap) {
                // 在 nowrap 模式下， 如果json化失败，则直接使用 字符串
                jsonObject = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
            }
            if (nowrap) {
                // 不对结果进行封装
                [actionObject callbackWithObject:jsonObject ?: @""];
            } else {
                // 封装成 V2 格式
                NSDictionary *resultMap = [self resolverCode:0 data:jsonObject message:nil];
                [actionObject callbackWithObject:resultMap];
            }
        } error:^(NSError *error) {
            NSMutableDictionary *dict = [NSMutableDictionary dictionary];
            dict[@"code"] = @(error.code);
            dict[@"message"] = error.debugDescription ?: @"";
            dict[@"error"] = [error.af_responseData imy_jsonObject] ?: @"";
            if (nowrap) {
                [actionObject callbackWithObject:dict];
            } else {
                NSDictionary *resultMap = [self resolverCode:-1 data:dict message:error.domain?:@""];
                [actionObject callbackWithObject:resultMap];
            }
        }];
    }];
    
    [[IMYURIManager shareURIManager] addForPath:@"mywtb/prefetch"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSDictionary *params = actionObject.uri.params;
        dispatch_async(IMYPrefetcher.sharedQueue, ^{
            NSString *urlString = params[@"url"];
            if (!urlString) {
                NSString *redirect_url = params[@"redirect_url"];
                urlString = [IMYURI uriWithURIString:redirect_url].params[@"url"];
            }
            if (imy_isNotBlankString(urlString)) {
                [IMYWebTemplateLoader prefetchLoadWithURL:[NSURL URLWithString:urlString]];
            }
        });
    }];
    
    
    /// sse/get协议说明文档: https://apidoc.seeyouyima.com/uri-docs/#/docs/6811d88c40b4504f59bc7258
    [[IMYURIManager sharedInstance] addForPath:@"sse/get" withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        
        NSDictionary *requestMap = actionObject.uri.params;
        NSDictionary *requestMapBackupForIusseDebug = [requestMap copy];
        NSString *url = requestMap[@"url"];
        NSDictionary *headers = requestMap[@"headers"];
        NSDictionary *params = requestMap[@"params"];
        NSInteger timeOutInterval = requestMap[@"timeOutInterval"] ? ([requestMap[@"timeOutInterval"] integerValue]) : 60;
        
        __block NSString *uuid = [[IMYEventSourceManager sharedInstance] startSSEGETRequest:url
                                                                                     params:params
                                                                                    headers:headers
                                                                            timeOutInterval:timeOutInterval
                                                                             onOpenCallback:^(LDEvent * _Nullable event) {
            
            NSDictionary *callbackData = @{
                @"state": @"open",
                @"req_id": uuid
            };
            [actionObject callbackWithObject:callbackData
                                       error:nil
                                   eventName:@"sse-callback"];
            
        } onReadyCallback:^(LDEvent * _Nullable event) {
            NSDictionary *callbackData = @{
                @"state": @"ready",
                @"req_id": uuid
            };
            [actionObject callbackWithObject:callbackData
                                       error:nil
                                   eventName:@"sse-callback"];
            
            
        } onMessageCallback:^(LDEvent * _Nullable event) {
            
            if ([event.data containsString:@"[DONE]"]) {
                
                NSDictionary *doneCallbackData = @{
                    @"state": @"done",
                    @"req_id": uuid
                };
                
                [actionObject callbackWithObject:doneCallbackData
                                           error:nil
                                       eventName:@"sse-callback"];
                
                // 当收到[DONE]后断开链接
                [[IMYEventSourceManager sharedInstance] cancelSSERequest:uuid];
                
                return;
                
            } else {
                
                // 假设这是从某个网络请求中接收到的 JSON 数据
                NSString *jsonString = event.data;
                
                if (imy_isEmptyString(jsonString)) {
                    NSString *assertMsg = [NSString stringWithFormat:@"%SSE GET流式服务端返回json数据为空!!!! -- [Info]: %@", (requestMapBackupForIusseDebug?:@{})];
                    NSAssert(NO, assertMsg);
                }
                
                // 将 JSON 字符串转换为 NSData
                NSData *jsonData = [jsonString dataUsingEncoding:NSUTF8StringEncoding];
                
                NSError *error = nil;
                id jsonObject = @{};
                
                // 使用 NSJSONSerialization 将 JSON 数据解析为 NSDictionary
                if (jsonData.length > 0) {
                    jsonObject = [NSJSONSerialization JSONObjectWithData:jsonData
                                                                 options:NSJSONReadingMutableContainers
                                                                   error:&error];
                }
                
                if (error) {
                    NSLog(@"解析 JSON 时出错: %@", error.localizedDescription);
                    
                    // 解析出错的情况下, 回调一个报错类型, 终止请求
                    
                    NSMutableDictionary *dict = [NSMutableDictionary dictionary];
                    dict[@"code"] = @(error.code);
                    dict[@"message"] = @"服务端返回json解析失败";
                    dict[@"error"] = error.localizedDescription ? : @"";
                    
                    
                    NSDictionary *errCallbackData = @{
                        @"state": @"error",
                        @"data": dict,
                        @"req_id": uuid,
                    };
                    
                    [actionObject callbackWithObject:errCallbackData
                                               error:error
                                           eventName:@"sse-callback"];
                    
                    
                    [[IMYEventSourceManager sharedInstance] cancelSSERequest:uuid];
                    
                    return;
                }
                
                NSDictionary *msgCallbackData = @{
                    @"state": @"message",
                    @"data": jsonObject,
                    @"req_id": uuid
                };
                
                [actionObject callbackWithObject:msgCallbackData
                                           error:nil
                                       eventName:@"sse-callback"];
                
            }
            
        } onErrorCallback:^(LDEvent * _Nullable event) {
            
            NSError *error = event.error;
            NSMutableDictionary *dict = [NSMutableDictionary dictionary];
            dict[@"code"] = @(error.code);
            dict[@"message"] = error.debugDescription ?: @"";
            dict[@"error"] = [error.af_responseData imy_jsonObject] ?: @"";
            
            
            NSDictionary *errCallbackData = @{
                @"state": @"error",
                @"data": dict,
                @"req_id": uuid,
            };
            
            [actionObject callbackWithObject:errCallbackData
                                       error:error
                                   eventName:@"sse-callback"];
            
        }];
        
        
        // 请求初始化回调
        NSDictionary *initCallbackData = @{
            @"state": @"init",
            @"req_id": uuid
        };
        
        [actionObject callbackWithObject:initCallbackData
                                   error:nil
                               eventName:@"sse-callback"];
        
    }];
    
    [[IMYURIManager sharedInstance] addForPath:@"sse/post" withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        
        NSDictionary *requestMap = actionObject.uri.params;
        NSDictionary *requestMapBackupForIusseDebug = [requestMap copy];
        NSString *url = requestMap[@"url"];
        NSDictionary *headers = requestMap[@"headers"];
        NSDictionary *params = requestMap[@"params"];
        NSInteger timeOutInterval = requestMap[@"timeOutInterval"] ? ([requestMap[@"timeOutInterval"] integerValue]) : 60;
        
        __block NSString *uuid = [[IMYEventSourceManager sharedInstance] startSSEPOSTRequest:url
                                                                                      params:params
                                                                                     headers:headers
                                                                             timeOutInterval:timeOutInterval
                                                                              onOpenCallback:^(LDEvent * _Nullable event) {
            
            NSDictionary *callbackData = @{
                @"state": @"open",
                @"req_id": uuid
            };
            [actionObject callbackWithObject:callbackData
                                       error:nil
                                   eventName:@"sse-callback"];
            
        } onReadyCallback:^(LDEvent * _Nullable event) {
            NSDictionary *callbackData = @{
                @"state": @"ready",
                @"req_id": uuid
            };
            [actionObject callbackWithObject:callbackData
                                       error:nil
                                   eventName:@"sse-callback"];
            
            
        } onMessageCallback:^(LDEvent * _Nullable event) {
            
            if ([event.data containsString:@"[DONE]"]) {
                
                NSDictionary *doneCallbackData = @{
                    @"state": @"done",
                    @"req_id": uuid
                };
                
                [actionObject callbackWithObject:doneCallbackData
                                           error:nil
                                       eventName:@"sse-callback"];
                
                // 当收到[DONE]后断开链接
                [[IMYEventSourceManager sharedInstance] cancelSSERequest:uuid];
                
                return;
                
            } else {
                
                // 假设这是从某个网络请求中接收到的 JSON 数据
                NSString *jsonString = event.data;
                
                if (imy_isEmptyString(jsonString)) {
                    NSString *assertMsg = [NSString stringWithFormat:@"%SSE POST流式服务端返回json数据为空!!!! -- [Info]: %@", (requestMapBackupForIusseDebug?:@{})];
                    NSAssert(NO, assertMsg);
                }
                
                
                // 将 JSON 字符串转换为 NSData
                NSData *jsonData = [jsonString dataUsingEncoding:NSUTF8StringEncoding];
                
                NSError *error = nil;
                id jsonObject = @{};
                
                // 使用 NSJSONSerialization 将 JSON 数据解析为 NSDictionary
                if (jsonData.length > 0) {
                    jsonObject = [NSJSONSerialization JSONObjectWithData:jsonData
                                                                 options:NSJSONReadingMutableContainers
                                                                   error:&error];
                }
                
                
                if (error) {
                    NSLog(@"解析 JSON 时出错: %@", error.localizedDescription);
                    
                    // 解析出错的情况下, 回调一个报错类型, 终止请求
                    
                    NSMutableDictionary *dict = [NSMutableDictionary dictionary];
                    dict[@"code"] = @(error.code);
                    dict[@"message"] = @"服务端返回json解析失败";
                    dict[@"error"] = error.localizedDescription ? : @"";
                    
                    
                    NSDictionary *errCallbackData = @{
                        @"state": @"error",
                        @"data": dict,
                        @"req_id": uuid,
                    };
                    
                    [actionObject callbackWithObject:errCallbackData
                                               error:error
                                           eventName:@"sse-callback"];
                    
                    
                    [[IMYEventSourceManager sharedInstance] cancelSSERequest:uuid];
                    
                    return;
                }
                
                NSDictionary *msgCallbackData = @{
                    @"state": @"message",
                    @"data": jsonObject,
                    @"req_id": uuid
                };
                
                [actionObject callbackWithObject:msgCallbackData
                                           error:nil
                                       eventName:@"sse-callback"];
                
            }
            
        } onErrorCallback:^(LDEvent * _Nullable event) {
            
            NSError *error = event.error;
            NSMutableDictionary *dict = [NSMutableDictionary dictionary];
            dict[@"code"] = @(error.code);
            dict[@"message"] = error.debugDescription ?: @"";
            dict[@"error"] = [error.af_responseData imy_jsonObject] ?: @"";
            
            
            NSDictionary *errCallbackData = @{
                @"state": @"error",
                @"data": dict,
                @"req_id": uuid,
            };
            
            [actionObject callbackWithObject:errCallbackData
                                       error:error
                                   eventName:@"sse-callback"];
            
        }];
        
        
        // 请求初始化回调
        NSDictionary *initCallbackData = @{
            @"state": @"init",
            @"req_id": uuid
        };
        
        [actionObject callbackWithObject:initCallbackData
                                   error:nil
                               eventName:@"sse-callback"];
        
    }];
    
    /// sse/cancel协议说明文档: https://apidoc.seeyouyima.com/uri-docs/#/docs/6811da7cef511d4f6bdf3035
    [[IMYURIManager sharedInstance] addForPath:@"sse/cancel" withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSDictionary *requestMap = actionObject.uri.params;
        NSString *uuid = requestMap[@"uuid"];
        [[IMYEventSourceManager sharedInstance] cancelSSERequest:uuid];
    }];
    
    
    [[IMYURIManager sharedInstance] addForPath:@"console/nslog" withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSDictionary *params = actionObject.uri.params;
        NSString *content = params[@"content"];
        NSLog(@"[H5-call-nslog]: %@", content);
    }];
    
}

#pragma mark -

+ (void)registerInfoAction {
    ///获取用户信息
    [[IMYURIManager shareURIManager] addForPath:@"userinfo/get"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSMutableDictionary *userinfo = [NSMutableDictionary dictionary];
        IMYPublicAppHelper *appHelper = [IMYPublicAppHelper shareAppHelper];
        
        userinfo[@"userid"] = appHelper.userid;
        userinfo[@"userMode"] = @(appHelper.userMode);
        userinfo[@"nickName"] = appHelper.nickName;
        userinfo[@"avatar"] = appHelper.avatar;
        userinfo[@"birthday"] = appHelper.birthday;
        userinfo[@"height"] = @(appHelper.height);
        userinfo[@"channelID"] = appHelper.channelID;
        userinfo[@"themeID"] = appHelper.themeID;
        userinfo[@"userToken"] = appHelper.userToken;
        userinfo[@"virtualToken"] = appHelper.virtualToken;
        userinfo[@"hasLogin"] = @(appHelper.hasLogin);
        userinfo[@"myclient"] = appHelper.myclient;
        if (NSBundle.enableMYAppInfo) {
            userinfo[@"myappinfo"] = appHelper.myappinfo;
        }
        userinfo[@"parsMensesDay"] = @(appHelper.parsMensesDay);
        userinfo[@"parsInterval"] = @(appHelper.parsInterval);
        userinfo[@"lastTimeMenses"] = appHelper.lastTimeMenses;
        userinfo[@"pregnancy"] = appHelper.pregnancy;
        userinfo[@"pregnancyStartDayDiff"] = @(appHelper.pregnancyStartDayDiff);
        userinfo[@"babyBirthday"] = appHelper.babyBirthday;
        userinfo[@"babyNick"] = appHelper.baby_nick;
        userinfo[@"babySex"] = @(appHelper.baby_sex);
        userinfo[@"exp"] = [IMYABTestManager sharedInstance].exp_log ?: @"";
        userinfo[@"isol"] = [IMYABTestManager sharedInstance].isol_log ?: @"";
        userinfo[@"recomm"] = @([IMYPublicAppHelper shareAppHelper].isPersonalRecommand);
        userinfo[@"young"] = @([IMYPublicAppHelper shareAppHelper].useYoungMode);
        userinfo[@"open-person-ad"] = @(appHelper.isPersonalAD);
        userinfo[@"open-person-eb-recomm"] = @(appHelper.isPersonalEBRecommend);
        userinfo[@"open-person-search-recom"] = @(!IMYHIVE_BINDER(IOCAppInfo).isClosedSearchRecommend);
        if (IMYHIVE_BINDER(IOCBabyInfo)) {
            userinfo[@"day_of_month"] = @([IMYHIVE_BINDER(IOCBabyInfo) day_of_month]);
            userinfo[@"month_of_year"] = @([IMYHIVE_BINDER(IOCBabyInfo) month_of_year]);
            userinfo[@"parenting_year"] = @([IMYHIVE_BINDER(IOCBabyInfo) parenting_year]);
            userinfo[@"parenting_info"] = @([IMYHIVE_BINDER(IOCBabyInfo) parenting_info]);
        }
        
        // 新柚宝宝使用小工具，这时候使用小工具的身份逻辑与原来身份逻辑不同，需要特殊处理
        if ([IMYPublicAppHelper isNewBaoBaoJi] && appHelper.bbjUserSecondModel) {
            //1.web协议回调，修改为当前的ybb_mode.
            //2.备用字段common_userMode,值为原先的userMode
            //3.业务逻辑根据userMode去取值的，代码修改。userMode
            
            // 保留原先的userMode
            userinfo[@"common_userMode"] = userinfo[@"userMode"];
            // 替换原本的userMode
            userinfo[@"userMode"] = @(appHelper.bbjUserSecondModel.userMode);
        }

        Class taeClass = NSClassFromString(@"IMYTAEManager");
        if ([taeClass respondsToSelector:@selector(taobaoUserId)]) {
            userinfo[@"tbuid"] = [taeClass performSelector:@selector(taobaoUserId)];
        }
        
        //7.8.0 新增 备孕周期、备孕周期天数
        if (appHelper.userMode == IMYVKUserModeForPregnant) {
            userinfo[@"phase"] = @(appHelper.phase);
            userinfo[@"phase_day"] = @(appHelper.phase_day);
        }
        
        // 7.6.9孕期需求
        IMYUserInfoModel *userSecondModel = appHelper.userSecondModel;
        if (userSecondModel.userMode) {
            userinfo[@"mode2"] = @(userSecondModel.userMode).stringValue;
        }
        if (userSecondModel.baby_id) {
            userinfo[@"bbid"] = @(userSecondModel.baby_id).stringValue;
        }
        if (imy_isNotBlankString(userSecondModel.birthday)) {
            userinfo[@"bbday"] = userSecondModel.birthday;
        }
        // 877 亲友模式
        if (userSecondModel.x_visit_mode) {
            userinfo[@"x-visit-mode"] = @(userSecondModel.x_visit_mode).stringValue;
        }
        
        [actionObject callbackWithObject:userinfo];
    }];
    
    ///获取是否开启了远程推送
    [[IMYURIManager shareURIManager] addForPath:@"device/remotePush"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        __weak IMYVKWebView *webView = actionObject.webView;
        void (^callbackBlock)(BOOL) = ^(BOOL enable) {
            imy_asyncMainExecuteBlock(^{
                NSDictionary *params = @{ @"enable": @(enable) };
                [actionObject callbackWithObject:params];
            });
        };
        if (IOS10) {
            [[UNUserNotificationCenter currentNotificationCenter] getNotificationSettingsWithCompletionHandler:^(UNNotificationSettings *settings) {
                callbackBlock(settings.authorizationStatus == UNAuthorizationStatusAuthorized);
            }];
        } else {
            UIUserNotificationType types = [[UIApplication sharedApplication] currentUserNotificationSettings].types;
            callbackBlock(types != UIUserNotificationTypeNone);
        }
    }];
    
    ///今天已签到
    [[IMYURIManager shareURIManager] addForPath:@"userinfo/youbi/signed"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        [IMYPublicAppHelper shareAppHelper].todaySigned = YES;
        dispatch_async(dispatch_get_main_queue(), ^{
            [[NSNotificationCenter defaultCenter] postNotificationName:kIMYURIYoubiRefreshNotification object:nil];
        });
    }];
    
    ///修改柚币数量
    [[IMYURIManager shareURIManager] addForPath:@"userinfo/youbi/refresh"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        dispatch_async(dispatch_get_main_queue(), ^{
            [[NSNotificationCenter defaultCenter] postNotificationName:kIMYURIYoubiRefreshNotification object:nil];
        });
    }];
    
    ///收藏贴士成功提醒
    [[IMYURIManager shareURIManager] addForPath:@"tip/collection/successs"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        if ([UIView showCollectSuccessHUD]) {
            [UIView imy_showTextHUD:IMYString(@"收藏成功")];
        }
    }];
    
    ///获取door接口配置
    [[IMYURIManager shareURIManager] addForPath:@"door"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSString *key = actionObject.uri.params[@"key"];
        IMYSwitchModel *model = [[IMYDoorManager sharedManager] switchForType:key];
        NSMutableDictionary *map = [NSMutableDictionary dictionary];
        if (model) {
            map[@"key"] = key;
            map[@"status"] = @(model.status);
            map[@"message"] = model.message;
            map[@"data"] = model.dataDictionary;
        }
        [actionObject callbackWithObject:map];
    }];
    
    ///获取当前设备的网络状态
    [[IMYURIManager shareURIManager] addForPath:@"device/network"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSDictionary *infoDict = [self currentNetowrkState];
        [actionObject callbackWithObject:infoDict];
    }];
    
    [[IMYURIManager shareURIManager] addForPath:@"register/networkchange"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        UIViewController *viewController = actionObject.getUsingViewController;
        [[[IMYNetState networkChangedSignal] takeUntil:viewController.rac_willDeallocSignal] subscribeNext:^(id x) {
            NSDictionary *infoDict = [self currentNetowrkState];
            [actionObject callbackWithObject:infoDict error:nil eventName:@"register/networkchange"];
        }];
    }];
    
    [[IMYURIManager shareURIManager] addForPath:@"appInfo/get"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSMutableDictionary *params = [NSMutableDictionary dictionary];
        IMYPublicAppHelper *appHelper = [IMYPublicAppHelper shareAppHelper];
        params[@"device_id"] = [UIDevice imy_macaddress];
        params[@"openudid"] = [UIDevice imy_openUDID];
        NSDictionary *statInfoHeaders = [IMYMeetyouHTTPHooks statInfoHeadersForUrlString:@"https://data.seeyouyima.com/v2/x"];
        [params addEntriesFromDictionary:statInfoHeaders];
        params[@"myclient"] = appHelper.myclient;
        if (NSBundle.enableMYAppInfo) {
            params[@"myappinfo"] = appHelper.myappinfo;
        }
        params[@"ua"] = [UIDevice imy_userAgent];
        params[@"themeid"] = appHelper.themeID;
        params[@"lang"] = appHelper.language;
        if (IMYHIVE_BINDER(IOCAppInfo).clang) {
            params[@"clang"] = IMYHIVE_BINDER(IOCAppInfo).clang;
        }
        params[@"scale"] = [NSString stringWithFormat:@"%.1f", SCREEN_SCALE];
        params[@"platform"] = [UIDevice imy_platform];
        params[@"source"] = [IMYMeetyouHTTPHooks currentPageSource];
        params[@"session_id"] = [IMYGAEventHelper sessionID];
        [self addAuthToParams:params];
        
        [actionObject callbackWithObject:params];
    }];
    
    [[IMYURIManager shareURIManager] addForPath:@"doorinfo/get"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        IMYABTestVariables *config = [[IMYCommonConfig sharedInstance] configForKey:@"enableDebugInfo"];
        if ([config boolForKey:@"enable"]) {
            NSDictionary *allMaps = [[IMYDoorManager sharedManager] valueForKey:@"allSwitchDictionary"];
            NSDictionary *jsonObject = [allMaps yy_modelToJSONObject];
            [actionObject callbackWithObject:jsonObject];
        } else {
            [actionObject callbackWithObject:@{@"msg": @"无授权"}];
        }
    }];
    
    [[IMYURIManager shareURIManager] addForPath:@"commoninfo/get"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        IMYABTestVariables *config = [[IMYCommonConfig sharedInstance] configForKey:@"enableDebugInfo"];
        if ([config boolForKey:@"enable"]) {
            NSDictionary *allMaps = [[IMYCommonConfig sharedInstance] valueForKey:@"map"];
            NSDictionary *jsonObject = [allMaps yy_modelToJSONObject];
            [actionObject callbackWithObject:jsonObject];
        } else {
            [actionObject callbackWithObject:@{@"msg": @"无授权"}];
        }
    }];
    
    /// 【code】当前用户帐号状态：0：正常，1：禁言，2：封号，3：封Mac，4：单圈禁言，5：被盗号
    /// 【reason】当前用户帐号状态原因：目前不可用
    [[IMYURIManager shareURIManager] addForPath:@"user/status/get" withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSMutableDictionary *map = [NSMutableDictionary dictionary];
        
        if (IMYHIVE_BINDER(IOCAppInfo)) {
            map[@"code"] = @([IMYHIVE_BINDER(IOCAppInfo) userStatusCode]);
            map[@"reason"] = @([IMYHIVE_BINDER(IOCAppInfo) userStatusReason]);
        }
        
        [actionObject callbackWithObject:map];
    }];
}

+ (BOOL)addAuthToParams:(NSMutableDictionary *)params {
    NSDictionary *authMap = [IMYMeetyouHTTPHooks currentAuthorizationMap];
    if (!authMap.count) {
        return NO;
    }
    NSString *token = authMap.allValues.firstObject;
    if (![token hasPrefix:@"XDS "] && ![token hasPrefix:@"VDS "]) {
        // 授权头格式不对
        return NO;
    }
    NSString *key = [token hasPrefix:@"XDS "] ? @"auth" : @"v_auth";
    params[key] = [token substringFromIndex:4];
    return YES;
}

+ (NSDictionary *)currentNetowrkState {
    NSMutableDictionary *infoDict = [NSMutableDictionary dictionary];
    if ([IMYNetState isWiFiValid]) {
        infoDict[@"state"] = @1;
    } else if ([IMYNetState is2G]) {
        infoDict[@"state"] = @2;
    } else if ([IMYNetState is3G]) {
        infoDict[@"state"] = @3;
    } else if ([IMYNetState is4G]) {
        infoDict[@"state"] = @4;
    } else if ([IMYNetState is5G]) {
        infoDict[@"state"] = @5;
    } else {
        infoDict[@"state"] = @0;
    }
    if ([IMYNetState networkEnable]) {
        infoDict[@"enable"] = @1;
    } else {
        infoDict[@"enable"] = @0;
    }
    return infoDict;
}

+ (NSString *)getURLWithActionObject:(IMYURIActionBlockObject *)actionObject {
    NSString *url = [actionObject.uri.params[@"url"] imy_trimString];
    if (imy_isBlankString(url)) {
        return nil;
    }
    IMYURI *newUri = [IMYURI uriWithURIString:url];
    if ([[IMYURIManager shareURIManager] containScheme:newUri.scheme]) {
        IMYURIActionBlockObject *newAction = [IMYURIActionBlockObject actionBlockWithURI:newUri];
        newAction.webView = actionObject.webView;
        newAction.userInfo = actionObject.userInfo;
        newAction.implCallbackBlock = actionObject.implCallbackBlock;
        BOOL hasAction = [[IMYURIManager shareURIManager] runActionWithActionObject:newAction completed:nil];
        if (hasAction) {
            return nil;
        }
    }
    url = [self fullURLWithWebURL:url baseURL:actionObject.webView.URL];
    return url;
}

#pragma mark - 其他uri注册

+ (void)registerWebJumpAction {
    void (^WebVCURIRegister)(IMYURIActionBlockObject *) = ^(IMYURIActionBlockObject *actionObject) {
        NSString *url = [self getURLWithActionObject:actionObject];
        if (!url.length) {
            return;
        }
        IMYVKWebViewController *webVC = [IMYVKWebViewController webWithURLString:url];
        /// 相应协议参数，会直接设置到 vc 属性中
        [webVC imy_setPropertyWithDictionary:actionObject.uri.params filter:@"url", nil];
        [webVC setFromURI:actionObject.uri];
        [[actionObject getUsingViewController] imy_push:webVC];
    };
    ///推一个新webVC进来
    [[IMYURIManager shareURIManager] addForPath:@"web" level:100 withActionBlock:WebVCURIRegister];
    [[IMYURIManager shareURIManager] addForPath:@"ebweb" level:100 withActionBlock:WebVCURIRegister];
    [[IMYURIManager shareURIManager] addForPath:@"ebweb/*" level:100 withActionBlock:WebVCURIRegister];
    [[IMYURIManager shareURIManager] addForPath:@"tae/web" level:100 withActionBlock:WebVCURIRegister];
    
    // 使用url鉴定协议
    [[IMYURIManager shareURIManager] addForPath:@"web/valid" withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSString *origURL = actionObject.uri.params[@"url"];
        NSString *validApi = [NSString stringWithFormat:@"%@/links/index.html", nodejs_user_seeyouyima_com];
        NSString *newUrl = [validApi imy_appendingURLParams:@{@"url":[origURL imy_URLEncode]}];
        [[IMYURIManager shareURIManager] runActionWithPath:@"web" params:@{@"url":newUrl} info:nil];
    }];
    
    // 加载本地HTML
    [[IMYURIManager shareURIManager] addForPath:@"local/web" withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        IMYVKLocalWebViewController *webVC = [IMYVKLocalWebViewController new];
        [webVC imy_setPropertyWithDictionary:actionObject.uri.params];
        [webVC setFromURI:actionObject.uri];
        [[actionObject getUsingViewController] imy_push:webVC];
    }];
    
    /**
     *  @brief  web/cool     , web/refresh  ,  web/cool/refresh  , web/removeself
     无topbar 或者 自带原生下拉刷新的webVC
     */
    void (^WebVCURIRegister2)(IMYURIActionBlockObject *) = ^(IMYURIActionBlockObject *actionObject) {
        NSString *url = [self getURLWithActionObject:actionObject];
        if (!url.length) {
            return;
        }
        NSString *path = actionObject.uri.path.lowercaseString;
        IMYVKWebViewController *webVC = nil;
        if ([path containsString:@"cool"]) {
            webVC = [IMYVKWebCoolViewController webWithURLString:url];
        } else {
            webVC = [IMYVKWebViewController webWithURLString:url];
        }
        if ([path containsString:@"refresh"]) {
            webVC.pullRefreshEnable = YES;
        }
        [webVC imy_setPropertyWithDictionary:actionObject.uri.params filter:@"url", nil];
        [webVC setFromURI:actionObject.uri];
        // 跳转新页面，并移除自己
        UIViewController *currentVC = actionObject.getUsingViewController;
        if ([path containsString:@"removeself"]) {
            IMYPublicBaseNavigationController *nav = (id)currentVC.imy_navigationController;
            if ([nav isKindOfClass:IMYPublicBaseNavigationController.class] &&
                !nav.canAction) {
                // 如果当前nav不可操作，则循环等待时机
                imy_asyncMainBlockInRunLoopModeRepeat(NSRunLoopCommonModes, NSIntegerMax, 0.3, ^BOOL(NSInteger index) {
                    if (nav.canAction) {
                        [currentVC imy_push:webVC];
                        [currentVC imy_removeSelfInNavigationController];
                        return YES;
                    }
                    return NO;
                });
            } else {
                // 可执行的情况，立即执行
                [currentVC imy_push:webVC];
                [currentVC imy_removeSelfInNavigationController];
            }
        } else {
            [currentVC imy_push:webVC];
        }
    };
    [[IMYURIManager shareURIManager] addForPath:@"web/*" level:100 withActionBlock:WebVCURIRegister2];
    [[IMYURIManager shareURIManager] addForPath:@"web/*/*" level:100 withActionBlock:WebVCURIRegister2];
    
    [[IMYURIManager shareURIManager] addForPath:@"web/fullscreen"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSString *url = [self getURLWithActionObject:actionObject];
        if (!url.length) {
            return;
        }
        IMYH5FullScreenVC *webVC = [IMYH5FullScreenVC webWithURLString:url];
        [webVC imy_setPropertyWithDictionary:actionObject.uri.params filter:@"url", nil];
        [webVC setFromURI:actionObject.uri];
        [webVC startMoveToRootView:nil];
    }];
    
    
    [[IMYURIManager shareURIManager] addForPath:@"pagesheet/web" withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSString *url = [self getURLWithActionObject:actionObject];
        if (!url.length) {
            return;
        }
        IMYVKPageSheetViewController *sheetVC = [IMYVKPageSheetViewController new];
        sheetVC.webUrl = url;
        [sheetVC imy_setPropertyWithDictionary:actionObject.uri.params filter:@"url", nil];
        sheetVC.fromVC = actionObject.getUsingViewController;
        sheetVC.fromURI = actionObject.uri;
        [sheetVC show:YES completion:nil];
    }];
    
    [[IMYURIManager shareURIManager] addForPath:@"pagesheet/dismiss" withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        IMYVKPageSheetViewController *sheetVC = actionObject.getUsingViewController.parentViewController;
        if (![sheetVC isKindOfClass:IMYVKPageSheetViewController.class]) {
            return;
        }
        NSString *goto_uri = actionObject.uri.params[@"goto_uri"];
        NSString *replace_from_web = actionObject.uri.params[@"replace_from_web"];
        BOOL animated = ![actionObject.uri.params[@"disable_anim"] boolValue];
        NSDictionary *callback_params = actionObject.uri.params[@"callback_params"];
        
        IMYVKWebViewController *fromWebVC = (id)sheetVC.fromVC;
        [sheetVC dismiss:animated completion:^{
            if (goto_uri.length > 0) {
                IMYURI *URI = [IMYURI uriWithURIString:goto_uri];
                if (fromWebVC) {
                    [URI appendingInfo:@{@"vc":fromWebVC}];
                }
                [[IMYURIManager shareURIManager] runActionWithURI:URI];
            }
            if (![fromWebVC isKindOfClass:IMYVKWebViewController.class]) {
                return;
            }
            if (replace_from_web.length > 0) {
                fromWebVC.urlString = replace_from_web;
            }
            if (callback_params.count > 0) {
                [fromWebVC.webView sendEvent:@"pagesheet/dismiss/action"
                                      params:callback_params];
            }
        }];
    }];
    
    [[IMYURIManager shareURIManager] addForPath:@"h5/fullscreen/show"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        IMYH5FullScreenVC *webVC = (id)[actionObject getUsingViewController];
        if ([webVC isKindOfClass:[IMYH5FullScreenVC class]]) {
            webVC.webHasShow = YES;
        }
    }];
    
    [[IMYURIManager shareURIManager] addForPath:@"h5/fullscreen/remove"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        IMYH5FullScreenVC *webVC = (id)[actionObject getUsingViewController];
        if ([webVC isKindOfClass:[IMYH5FullScreenVC class]]) {
            webVC.webHasRemoved = YES;
        }
    }];
    
    [[IMYURIManager shareURIManager] addForPath:@"open"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSString * const url = actionObject.uri.params[@"url"];
        if (imy_isEmptyString(url)) {
            [actionObject callbackWithObject:@(NO)];
            return;
        }
        NSString * const fail_uri = actionObject.uri.params[@"fail_uri"];
        NSNumber * const only_ulk = actionObject.uri.params[@"only_ulk"];
        
        NSURL * const openURL = [NSURL imy_URLWithString:url];
        NSDictionary *options = nil;
        if (only_ulk != nil) {
            options = @{
                UIApplicationOpenURLOptionUniversalLinksOnly : @(only_ulk.boolValue),
            };
        }
        [[UIApplication sharedApplication] imy_openURL:openURL
                                               options:options
                                     completionHandler:^(BOOL success) {
            imy_asyncMainExecuteBlock(^{
                [actionObject callbackWithObject:@(success)];
                // 打开失败，执行降级URI
                if (!success && fail_uri.length > 0) {
                    [[IMYURIManager sharedInstance] runActionWithString:fail_uri];
                }
            });
        }];
    }];
    
    [[IMYURIManager shareURIManager] addForPath:@"canOpen"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSString *url = actionObject.uri.params[@"url"];
        BOOL result = NO;
        if (imy_isNotBlankString(url)) {
            result = [[UIApplication sharedApplication] canOpenURL:[NSURL URLWithString:url]];
        }
        [actionObject callbackWithObject:@(result)];
    }];
    
    [[IMYURIManager shareURIManager] addForPath:@"web/platformAd"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSString *url = actionObject.uri.params[@"url"];
        BOOL myid = [actionObject.uri.params[@"myid"] boolValue];
        if (myid && [IMYPublicAppHelper shareAppHelper].myid.length) {
            url = [url imy_appendingURLParams:@{@"myid": [IMYPublicAppHelper shareAppHelper].myid}];
        }
        IMYOtherPlatformWebViewController *webVC = [IMYOtherPlatformWebViewController webWithURLString:url];
        webVC.userInfo = actionObject.uri.params;
        [webVC imy_setPropertyWithDictionary:actionObject.uri.params filter:@"url", nil];
        [webVC setFromURI:actionObject.uri];
        [actionObject.getUsingViewController imy_push:webVC];
    }];
    [[IMYURIManager shareURIManager] addForPath:@"web/pure"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSString *url = [self getURLWithActionObject:actionObject];
        if (!url.length) {
            return;
        }
        // 可能从第三方进来，会打开相同的页面，如果url重复，要移除原来的那个页面
        UIViewController *preVC = [actionObject getUsingViewController];
        // push 新页面
        IMYVKWebPureViewController *webVC = [IMYVKWebPureViewController webWithURLString:url];
        [webVC imy_setPropertyWithDictionary:actionObject.uri.params filter:@"url", nil];
        [webVC setFromURI:actionObject.uri];
        if (webVC.orientation != 0) {
            // 通过这种方式才能实现设备不旋转时，界面自动横屏
            if (preVC.presentingViewController) { // 前一个被present
                [preVC imy_push:webVC];
            } else {
                IMYPublicBaseNavigationController *nav = [[IMYPublicBaseNavigationController alloc] initWithRootViewController:webVC];
                [preVC.navigationController presentViewController:nav animated:YES completion:nil];
            }
        } else {
            BOOL present = [[actionObject.uri.params objectForKey:@"present"] boolValue];
            if (present) {
                IMYPublicBaseNavigationController *nav = [[IMYPublicBaseNavigationController alloc] initWithRootViewController:webVC];
                [preVC presentViewController:nav animated:YES completion:nil];
            }else{
                [preVC imy_push:webVC];
            }
        }
    }];
    
    [[IMYURIManager sharedInstance] addForPath:@"web/pure/common" withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        [actionObject.uri appendingParams:@{
            @"immersive": @(1),
            @"bounceStyle": @(0),
            @"hideShowLeftCool": @(1),
            @"usingWK": @(1),
            @"simple": @(1)
        }];
        [[IMYURIManager sharedInstance] runActionWithPath:@"web/pure" params:actionObject.uri.params info:actionObject.userInfo];
    }];
    
    [[IMYURIManager shareURIManager] addForPath:@"content/inset/adjustment"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        /*
         由于苹果新系统内部不知道做了啥改动，导致这个异常，经过排查发现 通过设置 scrollView.contentInsetAdjustmentBehavior 能修复，
         但是一些老旧页面开启后会变的异常， 所以只能是有需要的页面进行 动态开启
         */
        if (@available(iOS 11, *)) {
            NSInteger adjustment = [actionObject.uri.params[@"adjustment"] integerValue] - 1;
            if (adjustment < UIScrollViewContentInsetAdjustmentAutomatic || adjustment > UIScrollViewContentInsetAdjustmentAlways) {
                adjustment = UIScrollViewContentInsetAdjustmentNever;
            }
            actionObject.webView.scrollView.contentInsetAdjustmentBehavior = adjustment;
        }
    }];
    
    [[IMYURIManager shareURIManager] addForPath:@"content/inset/bottom"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        if (!iPhoneX) {
            return;
        }
        NSString *colorStr = actionObject.uri.params[@"color"] ?: kIMY_BG;
        UIColor *color = [UIColor imy_colorForKey:colorStr];
        actionObject.webView.safeBottomMarginBar.backgroundColor = color;
    }];
    
    [[IMYURIManager shareURIManager] addForPath:@"remove/webpage" withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        // 过滤关键字
        NSString * const like = actionObject.uri.params[@"like"];
        UIViewController *currentVC = actionObject.getUsingViewController;
        NSArray *childVCs = currentVC.imy_navigationController.viewControllers;
        // 默认移除当前页面 + 往前追溯 like 页面
        NSRange willRemoveRange = NSMakeRange(childVCs.count - 1, 1);
        if (imy_isNotBlankString(like)) {
            // 从后往前寻找，寻找第一个非like页面
            for (NSInteger i = childVCs.count - 2; i >= 0; i --) {
                UIViewController *webVC = [childVCs imy_objectAtIndex:i];
                // 判断当前 page url 是否包含关键字， i == 0 (遍历到头)
                BOOL hitH5 = ([webVC isKindOfClass:IMYVKWebViewController.class] &&
                              [((IMYVKWebViewController *)webVC).webView.likelyURL.absoluteString containsString:like]);
                BOOL hitRN = ([webVC isKindOfClass:NSClassFromString(@"IMYRNViewController")] &&
                              [[webVC valueForKeyPath:@"initialProperties.url"] containsString:like]);
                // 需要过滤 H5 和 RN 两种类型的页面
                if (!(hitH5 || hitRN) || i == 0) {
                    willRemoveRange.location = i + 1;
                    willRemoveRange.length = childVCs.count - willRemoveRange.location;
                    break;
                }
            }
        }
        // 根页面不能移除
        if (willRemoveRange.length > 0 && willRemoveRange.location == 0) {
            willRemoveRange.location = 1;
            willRemoveRange.length = childVCs.count - 1;
        }
        // 判断是否有新页面要跳转
        NSString *uriString = actionObject.uri.params[@"uri"];
        if (imy_isNotBlankString(uriString)) {
            IMYURI *URI = [IMYURI uriWithURIString:uriString];
            [URI appendingInfo:@{ @"vc" : currentVC }];
            [[IMYURIManager shareURIManager] runActionWithURI:URI];
            // remove 必须放在 push vc 代码之后，这样不会影响动画
            if (willRemoveRange.length > 0) {
                NSMutableArray *mutableArray = [currentVC.imy_navigationController.viewControllers mutableCopy];
                [mutableArray removeObjectsInRange:willRemoveRange];
                [currentVC.imy_navigationController setViewControllers:mutableArray animated:NO];
            }
        } else if (willRemoveRange.length > 0) {
            // 无 push 新页面，走 popToViewController 流程
            UIViewController *vc = [childVCs imy_objectAtIndex:willRemoveRange.location - 1];
            [currentVC.imy_navigationController popToViewController:vc animated:YES];
        }
    }];
}

+ (void)registerNavgationAction {
    ///返回上一级
    [[IMYURIManager shareURIManager] addForPath:@"goback"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSInteger count = [actionObject.uri.params[@"count"] integerValue];
        BOOL animated = YES;
        if (actionObject.uri.params[@"animated"]) {
            animated = [actionObject.uri.params[@"animated"] boolValue];
        }
        
        UINavigationController *navController = [actionObject.getUsingViewController imy_navigationController];
        if (count <= 1) {
            [navController imy_pop:animated];
        } else {
            NSInteger allCount = navController.viewControllers.count;
            if (count >= (allCount - 1)) {
                [navController popToRootViewControllerAnimated:animated];
            } else {
                UIViewController *toVC = navController.viewControllers[allCount - count - 1];
                [navController popToViewController:toVC animated:animated];
            }
        }
    }];
    
    ///退到最顶层
    [[IMYURIManager shareURIManager] addForPath:@"goback/top"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        UINavigationController *nav = [actionObject.getUsingViewController imy_navigationController];
        if (nav.viewControllers.count > 1) {
            [nav popToRootViewControllerAnimated:YES];
        } else {
            [nav dismissViewControllerAnimated:YES completion:nil];
        }
    }];
    
    ///如果是 present 优先dismiss
    [[IMYURIManager shareURIManager] addForPath:@"goback/dismiss"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        UINavigationController *nav = [actionObject.getUsingViewController imy_navigationController];
        if (nav.presentingViewController) {
            [nav dismissViewControllerAnimated:YES completion:nil];
        } else {
            [nav popToRootViewControllerAnimated:YES];
        }
    }];
    
    ///移除自己
    [[IMYURIManager shareURIManager] addForPath:@"removeself"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        [actionObject.getUsingViewController imy_removeSelfInNavigationController];
    }];
    
    // 支持H5动态控制：容器是否支持滚动
    [[IMYURIManager shareURIManager] addForPath:@"webscroll/enable" withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        id enable = actionObject.uri.params[@"enable"];
        if (actionObject.webView && enable) {
            actionObject.webView.scrollView.scrollEnabled = [enable boolValue];
        }
    }];
}

+ (void)registerTopbarAction {
    {
        void (^actionBlock)(id) = ^(IMYURIActionBlockObject *actionObject) {
            UIViewController *vc = [actionObject getUsingViewController];
            [vc imyuri_setTopbarButtonWithURIAction:actionObject];
        };
        [[IMYURIManager shareURIManager] addForPath:@"topbar/leftButton" withActionBlock:actionBlock];
        [[IMYURIManager shareURIManager] addForPath:@"topbar/leftButton/list" withActionBlock:actionBlock];
        [[IMYURIManager shareURIManager] addForPath:@"topbar/rightButton" withActionBlock:actionBlock];
        [[IMYURIManager shareURIManager] addForPath:@"topbar/rightButton/list" withActionBlock:actionBlock];
    }
    [[IMYURIManager shareURIManager] addForPath:@"topbar/bottomLine"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        UIViewController *vc = [actionObject getUsingViewController];
        BOOL isHide = [actionObject.uri.params[@"hide"] boolValue];
        if ([vc isKindOfClass:IMYPublicBaseViewController.class]) {
            ((IMYPublicBaseViewController *)vc).hideNavBarBottomLine = isHide;
        }
    }];
    
    [[IMYURIManager shareURIManager] addForPath:@"topbar/title"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        UIViewController *vc = [actionObject getUsingViewController];
        NSString *title = actionObject.uri.params[@"title"];
        NSString *image = actionObject.uri.params[@"image"];
        if (!imy_isEmptyString(image) && [vc isKindOfClass:[IMYVKWebViewController class]]) {
            [(IMYVKWebViewController *)vc setNavigationBarTitle:title titleIcon:image];
        } else {
            [vc imyuri_setTopbarTitle:title animated:[actionObject.uri.params[@"animation"] boolValue]];
        }
    }];
    
    [[IMYURIManager shareURIManager] addForPath:@"pullRefresh/close"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        UIViewController *currentVC = [actionObject getUsingViewController];
        [currentVC imyuri_setPullRefreshEnable:NO];
    }];
    
    [[IMYURIManager shareURIManager] addForPath:@"pullRefresh/open"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        UIViewController *currentVC = [actionObject getUsingViewController];
        [currentVC imyuri_setPullRefreshEnable:YES];
    }];
    
    [[IMYURIManager shareURIManager] addForPath:@"pullRefresh/end"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        UIViewController *currentVC = [actionObject getUsingViewController];
        [currentVC imyuri_setPullRefreshAnimationStart:NO];
    }];
    
    [[IMYURIManager shareURIManager] addForPath:@"pullRefresh/start"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        UIViewController *currentVC = [actionObject getUsingViewController];
        [currentVC imyuri_setPullRefreshAnimationStart:YES];
    }];
}

+ (void)registerViewConfig {
    [[IMYURIManager shareURIManager] addForPath:@"maskView/alpha"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        UIViewController *vc = [actionObject getUsingViewController];
        CGFloat value = [actionObject.uri.params[@"value"] floatValue];
        [vc imyuri_setMaskViewAlpha:value];
    }];
    
    [[IMYURIManager shareURIManager] addForPath:@"night/mask/enable"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        IMYPublicBaseViewController *baseVC = actionObject.getUsingViewController;
        if ([baseVC respondsToSelector:@selector(setEnableAutoNightMask:)]) {
            // 支持 RN、H5 统一蒙版
            BOOL enable = [actionObject.uri.params[@"enable"] boolValue];
            [baseVC setEnableAutoNightMask:enable];
        }
    }];
    
    // 跳到当前应用 权限设置页
    [[IMYURIManager shareURIManager] addForPath:@"app/setting"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        [[UIApplication sharedApplication] openURL:[NSURL URLWithString:UIApplicationOpenSettingsURLString] options:@{} completionHandler:^(BOOL success) {
            // OK
        }];
    }];
    
    [[IMYURIManager shareURIManager] addForPath:@"toast"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSString *message = [actionObject.uri.params objectForKey:@"message"];
        if (imy_isNotEmptyString(message)) {
            [UIView imy_showTextHUD:message];
        }
    }];
    
    [[IMYURIManager shareURIManager] addForPath:@"toast/show"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSString *text = actionObject.uri.params[@"text"];
        BOOL isLoading = [actionObject.uri.params[@"loading"] boolValue];
        if (isLoading) {
            [UIWindow imy_showLoadingHUDWithText:text];
        } else {
            [UIView imy_showTextHUD:text];
        }
    }];
    
    [[IMYURIManager shareURIManager] addForPath:@"toast/hide"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        [UIWindow imy_hideHUD];
    }];
    
    [[IMYURIManager shareURIManager] addForPath:@"statusBar/change"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSDictionary *statusBarInfo = actionObject.uri.params;
        UIViewController *currentVC = actionObject.getUsingViewController;
        NSNumber *hideNum = statusBarInfo[@"hide"];
        if (hideNum) {
            currentVC.imy_config.prefersStatusBarHidden = hideNum.boolValue;
        }
        NSNumber *styleNum = statusBarInfo[@"style"];
        if (styleNum.integerValue > 0) {
            currentVC.imy_config.preferredStatusBarStyle = styleNum.integerValue - 1;
        }
    }];
}

+ (void)registerKeyboardStatus {
    static BOOL kIMYKeyboardShowing = NO;
    static double kIMYKeyboardHeight = 0;
    RACSubject *kIMYKeyboardChangedSubject = [RACSubject subject];
    [[NSNotificationCenter defaultCenter] addObserverForName:UIKeyboardDidShowNotification
                                                      object:nil
                                                       queue:nil
                                                  usingBlock:^(NSNotification *_Nonnull note) {
        if (kIMYKeyboardShowing) {
            // 已处于键盘弹起，无需执行后续逻辑
            return;
        }
        CGRect rect = [note.userInfo[UIKeyboardFrameEndUserInfoKey] CGRectValue];
        kIMYKeyboardHeight = rect.size.height;
        kIMYKeyboardShowing = YES;
        [kIMYKeyboardChangedSubject sendNext:nil];
    }];
    [[NSNotificationCenter defaultCenter] addObserverForName:UIKeyboardDidHideNotification
                                                      object:nil
                                                       queue:nil
                                                  usingBlock:^(NSNotification *_Nonnull note) {
        if (!kIMYKeyboardShowing) {
            // 无键盘弹起，无需执行后续逻辑
            return;
        }
        kIMYKeyboardHeight = 0;
        kIMYKeyboardShowing = NO;
        [kIMYKeyboardChangedSubject sendNext:nil];
    }];
    
    // 前端实时获取键盘状态
    [[IMYURIManager shareURIManager] addForPath:@"keyboard/getStatus" withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSDictionary *params = @{
            @"isShow":@(kIMYKeyboardShowing),
            @"height":@(kIMYKeyboardHeight),
        };
        [actionObject callbackWithObject:params];
    }];
    
    // 前端监听键盘变化事件
    [[IMYURIManager shareURIManager] addForPath:@"keyboard/onChanged" withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        UIViewController *viewController = actionObject.getUsingViewController;
        [[kIMYKeyboardChangedSubject takeUntil:viewController.rac_willDeallocSignal] subscribeNext:^(id x) {
            NSDictionary *params = @{
                @"isShow":@(kIMYKeyboardShowing),
                @"height":@(kIMYKeyboardHeight),
            };
            [actionObject callbackWithObject:params error:nil eventName:@"keyboard/onChanged"];
        }];
    }];
}

+ (void)registerUserAddress {
    static IMYURIActionBlockObject *sender = nil;
    [[IMYURIManager shareURIManager] addForPath:@"user/address"
                                          level:-1
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        IMYSwitchModel *mode = [[IMYDoorManager sharedManager] switchForType:@"address_config"];
        NSString *h5_url = mode.dataDictionary[@"h5_url"];
        if (mode.status && imy_isNotBlankString(h5_url)) {
            // 走最新的H5地址
            NSNumber *callFromNative = actionObject.uri.params[@"callFromNative"];
            if (callFromNative.boolValue) {
                h5_url = [h5_url imy_appendingURLParams:@"fromNative=1"];
            }
            [[IMYURIManager shareURIManager] runActionWithPath:@"web" params:@{@"url": h5_url} info:nil];
            sender = actionObject;
            // 当 sender webView 释放的时候，清空全局变量
            [actionObject.webView.rac_willDeallocSignal subscribeCompleted:^{
                sender = nil;
            }];
        } else {
            // 不拦截
            actionObject.hasStop = NO;
        }
    }];
    
    [[IMYURIManager shareURIManager] addForPath:@"user/address/selected"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        // 由于历史原因，注册事件的时候，也会走进来，但是没有参数
        if (actionObject.uri.params.count == 0) {
            return;
        }
        [sender callbackWithObject:actionObject.uri.params];
        [actionObject callbackWithObject:actionObject.uri.params];
    }];
}

#pragma mark - viewKit 控件调用

+ (void)registerViewKitAction {
    [[IMYURIManager shareURIManager] addForPath:@"viewKit/sheet" withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSString *title = actionObject.uri.params[@"title"];
        NSString *cancelTitle = actionObject.uri.params[@"cancelTitle"]?:IMYString(@"取消");
        NSArray *actions = actionObject.uri.params[@"actions"];
        [IMYActionSheet sheetWithCancelTitle:cancelTitle otherTitles:actions summary:title showInView:nil action:^(NSInteger index) {
            [actionObject callbackWithObject:@{@"actionIndex":@(index)}];
        }];
    }];
    [[IMYURIManager shareURIManager] addForPath:@"viewKit/alert" withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSString *title = actionObject.uri.params[@"title"];
        NSString *message = actionObject.uri.params[@"message"]?:nil;
        NSString *cancelTitle = actionObject.uri.params[@"cancelTitle"]?:IMYString(@"取消");
        NSArray *actions = actionObject.uri.params[@"actions"];
        [UIAlertController imy_showAlertViewWithTitle:title
                                        message:message
                              cancelButtonTitle:cancelTitle
                              otherButtonTitles:actions
                                        handler:^(UIAlertController *alertController, NSInteger buttonIndex) {
                                            [actionObject callbackWithObject:@{@"actionIndex":@(buttonIndex)}];
                                        }];
    }];

}

#pragma mark - 图片相关

+ (void)registerImageURI {
    // 查看大图
    [[IMYURIManager shareURIManager] addForPath:@"gallery"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        [IMYURIAlbumOperator galleryPhotoWithURIAction:actionObject];
    }];
    
    // 选择图片
    [[IMYURIManager shareURIManager] addForPath:@"album/selector"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        [IMYURIAlbumOperator showPhotoSelectorWithURIAction:actionObject];
    }];
    
    // 选择图片
    [[IMYURIManager shareURIManager] addForPath:@"album/selector/crop/topTip"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        IMYURI *uri = actionObject.uri;
        NSMutableDictionary *params = [[NSMutableDictionary alloc] initWithDictionary:actionObject.uri.params];
//        [params setObject:@"剩3次机会，选择1张宝宝的正面彩超照" forKey: @"topTipText"  ];
//        [params setObject:@"请选择宝宝面部" forKey: @"topTipCropText"];
        [params setObject: @(YES) forKey:@"crop" ];
        [params setObject: @(YES) forKey:@"noShowSheet" ];
        [uri appendingParams:params];

        [IMYURIAlbumOperator showPhotoSelectorWithURIAction:actionObject];
    }];
    
    // 上传图片
    [[IMYURIManager shareURIManager] addForPath:@"oss/upload"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        /// 使用异步线程包下, 读取 image data 还是比较耗时的
        imy_asyncBlock(^{
            [IMYURIAlbumOperator ossUploadWithURIAction:actionObject];
        });
    }];
    
    // 批量上传asset图片
    [[IMYURIManager shareURIManager] addForPath:@"oss/upload/assetBatch"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        [IMYURIAlbumOperator ossBatchAssetsUploadWithURIAction:actionObject];
    }];
    
    // 是否有相册权限
    [[IMYURIManager shareURIManager] addForPath:@"album/authorized"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSInteger status = 0;
        const PHAuthorizationStatus authorStatus = [PHPhotoLibrary authorizationStatus];
        if (authorStatus == PHAuthorizationStatusRestricted || authorStatus == PHAuthorizationStatusDenied) {
            status = 1;
        } else if (authorStatus == PHAuthorizationStatusAuthorized) {
            status = 2;
        }
        NSDictionary *info = @{ @"status": @(status) };
        [actionObject callbackWithObject:info];
    }];
    
    // 是否有相机权限
    [[IMYURIManager shareURIManager] addForPath:@"camera/authorized"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        if (![UIImagePickerController isSourceTypeAvailable:UIImagePickerControllerSourceTypeCamera]) {
            // 设备不支持相机
            [actionObject callbackWithObject:@{ @"status": @(1) }];
            return;
        }
        NSInteger status = 0;
        const AVAuthorizationStatus authorStatus = [AVCaptureDevice authorizationStatusForMediaType:AVMediaTypeVideo];
        if (authorStatus == AVAuthorizationStatusRestricted || authorStatus == AVAuthorizationStatusDenied) {
            status = 1;
        } else if (authorStatus == AVAuthorizationStatusAuthorized) {
            status = 2;
        }
        NSDictionary *info = @{ @"status": @(status) };
        [actionObject callbackWithObject:info];
    }];
    
    // 申请相机权限
    [[IMYURIManager shareURIManager] addForPath:@"camera/request/permission"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        if (![UIImagePickerController isSourceTypeAvailable:UIImagePickerControllerSourceTypeCamera]) {
            // 设备不支持相机
            [actionObject callbackWithObject:@{ @"status": @(1) }];
            return;
        }
        // 向用户申请权限
        [AVCaptureDevice requestAccessForMediaType:AVMediaTypeVideo completionHandler:^(BOOL granted) {
            imy_asyncMainBlock(^{
                NSInteger status = granted ? 2 : 1;
                NSDictionary *info = @{ @"status": @(status) };
                [actionObject callbackWithObject:info];
            });
        }];
    }];
    
    // 是否有定位权限
    [[IMYURIManager shareURIManager] addForPath:@"gps/authorized"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        // 默认未授权
        NSInteger status = 0;
        const CLAuthorizationStatus authorizationStatus = [CLLocationManager authorizationStatus];
        if (authorizationStatus == kCLAuthorizationStatusAuthorizedAlways ||
            authorizationStatus == kCLAuthorizationStatusAuthorizedWhenInUse) {
            // 已同意
            status = 2;
        } else if (authorizationStatus == kCLAuthorizationStatusDenied) {
            // 已拒绝
            status = 1;
        }
        NSDictionary *info = @{ @"status" : @(status) };
        [actionObject callbackWithObject:info];
    }];
    
    // 获取定位信息
    [[IMYURIManager shareURIManager] addForPath:@"gps/get"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSMutableDictionary *dict = [NSMutableDictionary dictionary];
        // 从 user defaults 中获取
        IMYUserDefaults *userDefaults = [IMYUserDefaults standardUserDefaults];
        NSDictionary *coordinate = [userDefaults objectForKey:@"com.seeyou.locationInfo.coordinate"];
        NSString *cityname = [userDefaults stringForKey:@"com.seeyou.locationInfo.cityname"];
        NSString *cityid = [userDefaults stringForKey:@"com.seeyou.locationInfo.cityid"];
        NSString *gpstime = [userDefaults stringForKey:@"com.seeyou.locationInfo.gpstime"];
        // 赋值
        dict[@"longtitude"] = coordinate[@"longitude"] ?: @"";
        dict[@"latitude"] = coordinate[@"latitude"] ?: @"";
        dict[@"cid"] = cityid ?: @"";
        dict[@"city"] = cityname ?: @"";
        dict[@"time"] = gpstime ?: @"";
        [actionObject callbackWithObject:dict];
    }];
    
    // 保存图片
    [[IMYURIManager shareURIManager] addForPath:@"album/save"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        [IMYURIAlbumOperator savePhotoWithURIAction:actionObject];
    }];
}

@end

@interface IMYURIAlbumOperator () <IMYAssetPickerControllerDelegate, UIImagePickerControllerDelegate, UINavigationControllerDelegate> {
    id _holder;
}
@property (nonatomic, strong) IMYURIActionBlockObject *actionObject;

// 存储属性
@property (nonatomic, assign) NSInteger saveErrorCode;
@property (nonatomic, assign) NSInteger saveCompletedCount;
@property (nonatomic, assign) NSInteger saveTotalCount;

// 选择照片
@property (nonatomic, assign) NSInteger pickCallbackType;
@property (nonatomic, assign) NSInteger pickUploadType;
@property (nonatomic, assign) IMYOSSUploadScene pickUploadScene;
@property (nonatomic, assign) BOOL pickBase64;
@property (nonatomic, assign) NSInteger pickLimit;
@property (nonatomic, assign) BOOL pickCrop;
@property (nonatomic, copy) NSString *pickComefrom;
@property (nonatomic, copy) NSString *pickChooseText;
@property (nonatomic, copy) NSArray<NSString *> *pickIdentifys;

@property (nonatomic, assign) BOOL noShowSheet;//不展示 sheet弹窗
@property (nonatomic, copy) NSString *topTipText;
@property (nonatomic, copy) NSString *topTipCropText;
@end

@implementation IMYURIAlbumOperator

+ (UIScrollView *)findScrollViewWithURIAction:(IMYURIActionBlockObject *)actionObject {
    UIScrollView *scrollView = actionObject.webView.scrollView;
    // 兼容资讯，寻找包容WebView 的scrollview容器
    if (!scrollView.scrollEnabled) {
        UIScrollView *parentScrollView = [actionObject.webView.superview imy_findParentViewWithClass:UIScrollView.class];
        if (parentScrollView != nil) {
            scrollView = parentScrollView;
        }
    }
    return scrollView;
}

- (void)holdSelf {
    _holder = self;
}

- (void)unholdSelf {
    // 延迟1秒，释放自己
    id holder = _holder;
    _holder = nil;
    imy_asyncMainBlock(1, ^{
        [holder class];
    });
}

+ (NSString *)ossFileNameWithIdentify:(NSString *)idenfity imageSize:(CGSize)imageSize {
    NSString *uid = [IMYPublicAppHelper shareAppHelper].userid ?: @"0";
    NSString *prefix = idenfity.length > 0 ? idenfity : [NSUUID UUID].UUIDString;
    NSString *fileName = [NSString stringWithFormat:@"%@-%@-%@_%ld_%ld.jpg", @"album_uri", uid, [prefix imy_sha1], (long)imageSize.width, (long)imageSize.height];
    return fileName;
}

#pragma mark - 查看大图

// 查看大图
+ (void)galleryPhotoWithURIAction:(IMYURIActionBlockObject *)actionObject {
    UIScrollView *scrollView = [self findScrollViewWithURIAction:actionObject];
    if (scrollView.isDragging || scrollView.isDecelerating) {
        // 用户操作过程中，不执行协议
        return;
    }
    
    NSURL *baseURL = actionObject.webView.URL;
    NSArray *images = actionObject.uri.params[@"images"];
    images = [images filter:^BOOL(NSString *element) {
        return imy_isNotBlankString(element);
    }];
    NSInteger index = [actionObject.uri.params[@"index"] integerValue];
    if (index < 0 || index >= images.count) {
        index = 0;
    }
    if (images.count == 0) {
        return;
    }
    NSArray *pointArray = actionObject.uri.params[@"point"];
    CGRect clickFrame = CGRectZero;
    if ([pointArray isKindOfClass:[NSArray class]] && [pointArray count] == 4) {
        clickFrame = CGRectMake([pointArray[0] floatValue], [pointArray[1] floatValue], [pointArray[2] floatValue], [pointArray[3] floatValue]);
        if (!CGRectEqualToRect(clickFrame, CGRectZero)) {
            // 兼容 Flutter 处理, Flutter 页面没有 ScrollView, 不进行 clickFrame 转换
            if (scrollView != nil) {
                clickFrame = [scrollView convertRect:clickFrame toView:UIApplication.sharedApplication.delegate.window];
            }
        }
    }
    __block NSInteger preloadCount = 0;
    NSArray *photos = [images map:^id _Nonnull(NSString *_Nonnull element) {
        IMYPhoto *photo = [IMYPhoto new];
        if ([element containsString:@"://"]) {
            // 网络图片
            photo.url = [NSURL URLWithString:element];
        } else {
            // 本地图片
            photo.assetModel = [[IMYAssetModel alloc] initWithIdentifier:element];
            photo.isBrowser = YES;
        }
        //这里只做一次预览的缓存加载，避免外部已加载的时候，查看大图时却用未加载的效果显示
        if (preloadCount == index) {
            if (photo.url) {
                // 远程图片
                SDWebImageManager *manager = [SDWebImageManager sharedManager];
                NSString *cacheKey = [manager cacheKeyForURL:photo.url];
                id image = [manager.imageCache imageFromMemoryCacheForKey:cacheKey];
                if (image) {
                    photo.sy_placeholder = image;
                }
                if (!image) {
                    id diskImage = [manager.imageCache imageFromDiskCacheForKey:cacheKey];
                    if (diskImage) {
                        photo.sy_placeholder = diskImage;
                    }
                }
            } else if (photo.assetModel) {
                // 本地图片
                photo.sy_placeholder = [photo.assetModel thumbnailImageWithSize:CGSizeMake(SCREEN_WIDTH, SCREEN_WIDTH)];
            }
        }
        preloadCount ++;
        return photo;
    }];
    IMYPhotoBrowser *browser = nil;
    BOOL isShowComment = [actionObject.uri.params[@"isShowComment"] boolValue];
    if (isShowComment) {
        // 新版UI带有自定义界面走这个逻辑，747需求
        browser = [IMYPhotoBrowser showWithPhotos:[photos mutableCopy]
                                          atIndex:index
                                         fromRect:clickFrame
                                 isNeedShowCicrle:NO
                                         showType:IMYBrowserTypeCustomView
                                         delegate:actionObject.webView.delegate];
    } else {
        browser = [IMYPhotoBrowser showWithPhotos:[photos mutableCopy]
                                          atIndex:index
                                         fromRect:clickFrame];
    }
    browser.pageControlStyle = IMYBrowserPageControlStyleText;
}

#pragma mark - 上传图片

+ (void)ossUploadWithURIAction:(IMYURIActionBlockObject *)actionObject {
    NSDictionary *requestMap = actionObject.uri.params;
    NSString *name = requestMap[@"name"];
    NSString *identify = requestMap[@"identify"];
    NSString *base64 = requestMap[@"base64"];
    NSString *contentType = requestMap[@"contentType"];
    NSInteger uploadType = [requestMap[@"uploadType"] integerValue];
    NSInteger uploadScene = [requestMap[@"uploadScene"] integerValue];
    NSData *imageData = nil;
    CGSize imageSize = CGSizeZero;
    @autoreleasepool {
        if (identify.length > 0) {
            IMYAssetModel *assetModel = [[IMYAssetModel alloc] initWithIdentifier:identify];
            UIImage *image = [[assetModel thumbnailImageWithSize:CGSizeMake(SCREEN_WIDTH, SCREEN_HEIGHT)] imy_fixOrientation];
            id<IMYImageCompressedResult> compressedResult = [image imy_compressedResult];
            imageSize = compressedResult.size;
            imageData = compressedResult.data;
        } else {
            NSData *decodeData = [base64 base64DecodedData];
            UIImage *image = [[[UIImage alloc] initWithData:decodeData] imy_fixOrientation];
            // 如果无法转为 UIImage，则使用原始 Data
            if (image != nil) {
                id<IMYImageCompressedResult> compressedResult = [image imy_compressedResult];
                imageSize = compressedResult.size;
                imageData = compressedResult.data;
            } else {
                imageData = decodeData;
            }
        }
    }
    if (name.length == 0) {
        name = [self ossFileNameWithIdentify:identify imageSize:imageSize];
    }
    id<IMYOSSFileObject> fileObject = [[IMYOSS defaultUploader] fileObjectWithName:name data:imageData];
    if (uploadType > 0) {
        fileObject.uploadType = uploadType;
    }
    if (uploadScene > 0) {
        fileObject.uploadScene = uploadScene;
    }
    if (contentType.length > 0) {
        fileObject.contentType = contentType;
    }
    [[IMYOSS defaultUploader] uploadObject:fileObject
                             progressBlock:nil
                            complatedBlock:^(id<IMYOSSFileObject> _Nonnull object, NSError *_Nullable error) {
        NSDictionary *dict = nil;
        if (object.url.absoluteString.length > 0) {
            dict = @{ @"success": @1,
                      @"url": object.url.absoluteString };
        } else {
            dict = @{ @"success": @0,
                      @"error": error.localizedDescription ?: @"" };
        }
        [actionObject callbackWithObject:dict];
    }];
}

#pragma mark - 批量上传IMYAssetModel图片
+ (void)ossBatchAssetsUploadWithURIAction:(IMYURIActionBlockObject *)actionObject {
    IMYURIAlbumOperator *operator = [IMYURIAlbumOperator new];
    operator.actionObject = actionObject;
    [operator runAssetUploadAction];
}

- (void)runAssetUploadAction {
    NSArray<IMYAssetModel *> *assets = self.actionObject.uri.params[@"assets"];
    if (assets.count == 0) {
        return;
    }
    // 避免自己被释放
    [self holdSelf];
    
    self.pickUploadType = [self.actionObject.uri.params[@"uploadType"] integerValue];
    
    self.pickUploadScene = [self.actionObject.uri.params[@"uploadScene"] integerValue];
    
    self.pickCallbackType = [self.actionObject.uri.params[@"callback"] integerValue];
    
    // 上传到CDN
    imy_asyncBlock(^{
        if (2 == self.pickCallbackType) {
            // 进度型回调
            [self pick_callback_uploadToCDN:assets];
        } else {
            // 完整型回调
            [self pick_new_uploadToCDN:assets];
        }
    });
}

#pragma mark - 存储图片

// 存储图片
+ (void)savePhotoWithURIAction:(IMYURIActionBlockObject *)actionObject {
    UIScrollView *scrollView = [self findScrollViewWithURIAction:actionObject];
    if (scrollView.isDragging || scrollView.isDecelerating) {
        // 用户操作过程中，不执行协议
        return;
    }
    
    IMYURIAlbumOperator *store = [IMYURIAlbumOperator new];
    store.actionObject = actionObject;
    [store runSaveAction];
}

- (void)runSaveFinished {
    BOOL success = (self.saveCompletedCount == self.saveTotalCount);
    if (self.saveErrorCode > 0) {
        // 有错误code就算失败
        success = NO;
    }
    NSDictionary *params = @{ @"success": @(success),
                              @"error": @(self.saveErrorCode) };
    [self.actionObject callbackWithObject:params];
    
    // 释放自己
    [self unholdSelf];
}

- (void)runSaveAction {
    // 避免自己被释放
    [self holdSelf];
    
    NSArray<NSString *> * const array = self.actionObject.uri.params[@"images"];
    self.saveTotalCount = array.count;
    if (self.saveTotalCount == 0) {
        [self runSaveFinished];
        return;
    }
    
    const IMYAssetAuthorizationStatus authorStatus = [IMYAssetsManager authorizationStatusForAccessLevel:1];
    if (authorStatus == IMYAssetAuthorizationStatusNotAuthorized) {
        ///无相册权限
        NSString *message = [NSString stringWithFormat:IMYString(@"为了保存照片，请在「iPhone设置>隐私>%@」中开启%@权限。"), IMYString(@"照片"), [IMYPublicAppHelper shareAppHelper].appName];
        NSArray *otherButtonTitles = @[@"确认"];
        [UIAlertController imy_showAlertViewWithTitle:nil
                                              message:message
                                    cancelButtonTitle:@"取消"
                                    otherButtonTitles:otherButtonTitles
                                              handler:^(UIAlertController *alertController, NSInteger buttonIndex) {
            if (buttonIndex == 1) {
                [[UIApplication sharedApplication] openURL:[NSURL URLWithString:UIApplicationOpenSettingsURLString] options:@{} completionHandler:^(BOOL success) {
                    // OK
                }];
            }
        }];
        imy_asyncMainBlock(^{
            self.saveErrorCode = 1;
            [self runSaveFinished];
        });
        return;
    }
    BOOL const isBase64 = [self.actionObject.uri.params[@"base64"] boolValue];
    if (isBase64) {
        // 本地 base64 图片数据
        for (NSString *base64Str in array) {
            NSData * const data = [base64Str base64DecodedData];
            UIImage *image = nil;
            if (data.length > 0) {
                image = [UIImage imageWithData:data];
            }
            if (image.size.width > 0) {
                [UIImage imy_saveToPhotosWithData:data
                                   complatedBlock:^(NSError *_Nullable error) {
                    if (error) {
                        self.saveErrorCode = 2;
                    }
                    self.saveCompletedCount ++;
                    if (self.saveCompletedCount == self.saveTotalCount) {
                        // 完成存储任务
                        [self runSaveFinished];
                    }
                }];
            } else {
                self.saveErrorCode = 3;
                self.saveCompletedCount ++;
                if (self.saveCompletedCount == self.saveTotalCount) {
                    // 完成存储任务
                    [self runSaveFinished];
                }
            }
        }
    } else {
        // 远程图片地址
        [UIImageView imy_downloadImageDataWithURLs:array
                                    complatedBlock:^(NSArray<NSData *> *imageDatas) {
            for (NSData *data in imageDatas) {
                if ([data isKindOfClass:[NSData class]]) {
                    [UIImage imy_saveToPhotosWithData:data
                                       complatedBlock:^(NSError *_Nullable error) {
                        if (error) {
                            self.saveErrorCode = 2;
                        }
                        self.saveCompletedCount ++;
                        if (self.saveCompletedCount == self.saveTotalCount) {
                            // 完成存储任务
                            [self runSaveFinished];
                        }
                    }];
                } else {
                    self.saveErrorCode = 3;
                    self.saveCompletedCount ++;
                    if (self.saveCompletedCount == self.saveTotalCount) {
                        // 完成存储任务
                        [self runSaveFinished];
                    }
                }
            }
        }];
    }
}

#pragma mark - 选择图片
static BOOL kIMYPickPhotoVCShowing = NO;
+ (void)showPhotoSelectorWithURIAction:(IMYURIActionBlockObject *)actionObject {
    // 选择照片中
    if (kIMYPickPhotoVCShowing) {
        return;
    }
    IMYURIAlbumOperator *picker = [IMYURIAlbumOperator new];
    picker.actionObject = actionObject;
    [picker runPickShowAction];
}

- (void)runPickShowDismiss {
    kIMYPickPhotoVCShowing = NO;
}

- (void)runPickShowFinished:(NSArray *)photos {
    // 结束协议, 并回调调用方
    imy_asyncMainExecuteBlock(^{
        NSDictionary *map = nil;
        if (!photos) {
            // 用户取消选择
            map = @{};
        } else {
            // 点击确定
            map = @{
                @"photos": photos
            };
        }
        [self.actionObject callbackWithObject:map];
    });
    
    // 释放自己
    [self unholdSelf];
}

- (void)runPickShowAction {
    // 持有自己，避免释放
    [self holdSelf];
    
    // 不能重复弹PickerVC
    kIMYPickPhotoVCShowing = YES;
    
    NSInteger limit = [self.actionObject.uri.params[@"limit"] integerValue];
    self.pickCrop = [self.actionObject.uri.params[@"crop"] boolValue];
    if (limit <= 0 || self.pickCrop) {
        // 如果开启裁剪模式，只能选择一张照片
        limit = 1;
    }
    self.pickLimit = limit;
    self.pickComefrom = self.actionObject.uri.params[@"comefrom"];
    self.pickCallbackType = [self.actionObject.uri.params[@"callback"] integerValue];
    self.pickUploadType = [self.actionObject.uri.params[@"uploadType"] integerValue];
    self.pickUploadScene = [self.actionObject.uri.params[@"uploadScene"] integerValue];
    self.pickBase64 = [self.actionObject.uri.params[@"base64"] boolValue];
    self.pickChooseText = self.actionObject.uri.params[@"chooseText"];
    self.pickIdentifys = self.actionObject.uri.params[@"identifys"];
    
    self.topTipText = self.actionObject.uri.params[@"topTipText"];
    self.topTipCropText = self.actionObject.uri.params[@"topTipCropText"];
    self.noShowSheet = [self.actionObject.uri.params[@"noShowSheet"] boolValue];

    UIViewController *rootVC = self.actionObject.getUsingViewController;
    if (!rootVC) {
        rootVC = [UIViewController imy_currentTopViewController];
    }
    //展示
    if (self.pickCrop && !self.noShowSheet) {
        // 如果是可裁剪的模式，需要弹窗询问拍照还是相册
        // rootVC 需要取最底层的
        rootVC = rootVC.tabBarController ?: (rootVC.navigationController ?: rootVC);
        [self pickShowCropStyleImagePickerInVC:rootVC];
    } else {
        [self pickShowNewStyleImagePickerInVC:rootVC];
    }
}

- (void)pickShowCropStyleImagePickerInVC:(UIViewController *)rootVC {
    NSMutableArray *actions = [NSMutableArray arrayWithObjects:IMYString(@"从手机相册选择"), IMYString(@"拍照"), nil];
    [IMYActionSheet sheetWithCancelTitle:IMYString(@"取消")
                             otherTitles:actions
                                 summary:nil
                              showInView:rootVC.view
                                  action:^(NSInteger index) {
        if (index == 0) {
            [self runPickShowDismiss];
            [self runPickShowFinished:nil];
            return;
        }
        if (index == 2 && [UIImagePickerController needAlertForType:UIImagePickerControllerSourceTypeCamera]) {
            // 无权限
            NSString *message = [UIImagePickerController alertStringForType:UIImagePickerControllerSourceTypeCamera];
            [UIAlertController imy_showAlertViewWithTitle:@""
                                                  message:message
                                        cancelButtonTitle:IMYString(@"确定")
                                        otherButtonTitles:nil
                                                  handler:^(UIAlertController *alertController, NSInteger buttonIndex){
                [self runPickShowDismiss];
                [self runPickShowFinished:nil];
                NSLog(@"ok");
            }];
            return;
        }
        if (index == 1) {
            // 打开相册
            [self pickShowNewStyleImagePickerInVC:rootVC];
        } else {
            // 打开摄像头
            UIImagePickerController *imagePickerController = [[UIImagePickerController alloc] init];
            imagePickerController.allowsEditing = YES;
            imagePickerController.automaticallyAdjustsScrollViewInsets = NO;
            imagePickerController.extendedLayoutIncludesOpaqueBars = NO;
            imagePickerController.edgesForExtendedLayout = UIRectEdgeLeft | UIRectEdgeBottom | UIRectEdgeRight;
            imagePickerController.navigationController.automaticallyAdjustsScrollViewInsets = NO;
            imagePickerController.navigationController.extendedLayoutIncludesOpaqueBars = NO;
            imagePickerController.navigationController.edgesForExtendedLayout = UIRectEdgeLeft | UIRectEdgeBottom | UIRectEdgeRight;
            imagePickerController.delegate = self;
            imagePickerController.modalPresentationStyle = UIModalPresentationOverCurrentContext;
            if ([UIImagePickerController isSourceTypeAvailable:UIImagePickerControllerSourceTypeCamera]) {
                imagePickerController.sourceType = UIImagePickerControllerSourceTypeCamera;
            } else {
                [UIWindow imy_showTextHUD:IMYString(@"你的设备没有摄像头!")];
                imagePickerController.sourceType = UIImagePickerControllerSourceTypePhotoLibrary;
            }
            [rootVC presentViewController:imagePickerController
                                 animated:YES
                               completion:nil];
        }
    }];
}

- (void)pickShowNewStyleImagePickerInVC:(UIViewController *)rootVC {
    IMYAssetPickerController *vc = [[IMYAssetPickerController alloc] init];
    vc.allowsMultipleSelection = YES;
    vc.styleType = IMYAssetPickerUITypeNew;
    vc.maximumNumberOfSelection = self.pickLimit;
    vc.tipTopText = self.topTipText;
    vc.topTipCropText = self.topTipCropText;
    vc.chooseText = self.pickChooseText;
    vc.delegate = self;
    if (self.pickCrop) {
        // 头像裁剪模式
        vc.styleType = IMYAssetPickerUITypeSingle;
        vc.ratioSize = CGSizeMake(SCREEN_WIDTH, SCREEN_WIDTH);
    }
    // 已选中的照片
    if (self.pickIdentifys.count > 0) {
        NSMutableArray *selectedAssets = [NSMutableArray array];
        for (NSString *identify in self.pickIdentifys) {
            IMYAssetModel *assetModel = [[IMYAssetModel alloc] initWithIdentifier:identify];
            if (assetModel.identifier) {
                // 判断协议传入的 identify 是否有效
                [selectedAssets addObject:assetModel];
            }
        }
        vc.selectedAssetArray = selectedAssets;
    }
    // 显示
    [rootVC imy_present:vc animated:YES];
    // 埋点
    [IMYEventHelper event:@"xtxcdy" attributes:@{@"来源": self.pickComefrom ?: @"其他"}];
}

- (void)pick_callback_uploadToCDN:(NSArray<IMYAssetModel *> *)assetArray {
    NSMutableArray *results = [NSMutableArray array];
    for (IMYAssetModel *asset in assetArray) {
        @autoreleasepool {
            NSInteger thumbSize = 300 / SCREEN_SCALE;
            UIImage *thumb = [[asset thumbnailImageWithSize:CGSizeMake(thumbSize, thumbSize)] imy_fixOrientation];
            NSString *thumbBase64 = [[thumb imy_toData] base64EncodedStringWithOptions:0];
            [results addObject:@{
                @"code" : @0,
                @"thumb" : thumbBase64 ?: @"",
                @"identify" : asset.identifier ?: @""
            }];
        }
    }
    /// 先回调一个缩略图
    NSDictionary *map = @{
        @"photos": results.copy
    };
    IMYURIActionBlockObject *uriActionObject = _actionObject;
    imy_asyncMainExecuteBlock(^{
        /// 进度型回调，无需提示框
        [UIView imy_hideHUD];
        [uriActionObject callbackWithObject:map];
    });
    
    /// 再进行CDN回调
    [self pick_new_uploadToCDN:assetArray];
}

- (void)pick_new_uploadToCDN:(NSArray<IMYAssetModel *> *)assetArray {
    NSString *dirName = [NSString stringWithFormat:@"album_selector_%ld", (long)CFAbsoluteTimeGetCurrent()];
    NSString *dirPath = [[NSString imy_tmpDirectory] stringByAppendingPathComponent:dirName];
    [[NSFileManager defaultManager] createDirectoryAtPath:dirPath withIntermediateDirectories:YES attributes:nil error:nil];
    
    NSMutableArray<NSString *> *files = [NSMutableArray array];
    for (IMYAssetModel *asset in assetArray) {
        @autoreleasepool {
            UIImage *image = [[asset thumbnailImageWithSize:CGSizeMake(SCREEN_WIDTH, SCREEN_HEIGHT)] imy_fixOrientation];
            NSString *filePath = [self pick_writeImage:image dirPath:dirPath identify:asset.identifier];
            [files addObject:filePath];
        }
    }
    [self pick_uploadImageFiles:files dirPath:dirPath assets:assetArray];
}

- (void)pick_new_uploadToBase64:(NSArray<IMYAssetModel *> *)assetArray {
    NSMutableArray *results = [NSMutableArray array];
    for (IMYAssetModel *asset in assetArray) {
        @autoreleasepool {
            UIImage *image = [[asset thumbnailImageWithSize:CGSizeMake(SCREEN_WIDTH, SCREEN_HEIGHT)] imy_fixOrientation];
            id<IMYImageCompressedResult> compressedResult = [image imy_compressedResult];
            NSData *imageData = compressedResult.data;
            NSString *base64String = [imageData base64EncodedStringWithOptions:0];
            if (0 == self.pickCallbackType) {
                if (base64String.length > 0) {
                    [results addObject:base64String];
                }
            } else {
                [results addObject:@{
                    @"code" : @(base64String.length > 0 ? 0 : -1),
                    @"base64" : base64String ?: @"",
                    @"identify" : asset.identifier ?: @""
                }];
            }
        }
    }
    [self pick_uploadImageSucessed:results];
}

- (NSString *)pick_writeImage:(UIImage *)image dirPath:(NSString *)dirPath identify:(NSString *)identify {
    id<IMYImageCompressedResult> compressedResult = [image imy_compressedResult];
    CGSize imageSize = compressedResult.size;
    NSString *fileName = [self.class ossFileNameWithIdentify:identify imageSize:imageSize];
    NSString *filePath = [dirPath stringByAppendingFormat:@"/%@", fileName];
    NSData *imageData = compressedResult.data;
    if (imageData.length > 0) {
        BOOL success = [imageData writeToFile:filePath atomically:YES];
        if (!success) { // 重试一次
            [imageData writeToFile:filePath atomically:YES];
        }
    }
    return filePath;
}

/// 上传对应地址的数据到 OSS
/// @param array 图片本地地址
/// @param dirPath 临时文件夹，上传完成后会删除
/// @param assets 图片相册原始model，如果上传失败，需要获取缩略图
- (void)pick_uploadImageFiles:(NSArray<NSString *> *)array dirPath:(NSString *)dirPath assets:(NSArray<IMYAssetModel *> *)assets {
    // 获取要上传的 bucket type
    NSArray<id<IMYOSSFileObject>> *fileObjects = [array map:^id _Nonnull(NSString *_Nonnull filePath) {
        NSString *name = filePath.lastPathComponent;
        id<IMYOSSFileObject> fileObject = [[IMYOSS defaultUploader] fileObjectWithName:name path:filePath];
        if (self.pickUploadType > 0) {
            fileObject.uploadType = self.pickUploadType;
        }
        if (self.pickUploadScene > 0) {
            fileObject.uploadScene = self.pickUploadScene;
        }
        return fileObject;
    }];
    // 记录上传前时间
    CFAbsoluteTime uploadBeginTS = CFAbsoluteTimeGetCurrent();
    [[IMYOSS defaultUploader] uploadAllObjects:fileObjects
                                 progressBlock:nil
                                complatedBlock:^(id<IMYOSSFileObject>  _Nonnull obj, NSError * _Nullable error) {
        if (2 != self.pickCallbackType) {
            // 非回调型，不进行任何处理
            return;
        }
        NSInteger index = [fileObjects indexOfObject:obj];
        IMYAssetModel *asset = [assets imy_objectAtIndex:index];
        
        NSString *urlString = [[obj url] absoluteString];
        NSDictionary *map = nil;
        if ([obj state] == IMYOSSFileStateCompleted && urlString.length > 0) {
            map = @{
                @"code" : @0,
                @"url" : urlString,
                @"identify" : asset.identifier ?: @""
            };
            if ([IMYPublicAppHelper isNewBaoBaoJi]) {
                //bi 埋点上报需要额外参数
                NSMutableDictionary *tempDict = [map mutableCopy];
                //获取图片文件大小
                long long fileSize = ((IMYAliyunOSSFileObject *)obj).fileSize;
                [tempDict imy_setNonNilObject:@(fileObjects.count) forKey:@"photo_number"];
                [tempDict imy_setNonNilObject:[NSString stringWithFormat:@"%.2f", fileSize/1024.0] forKey:@"size"];
                [tempDict imy_setNonNilObject:@(CFAbsoluteTimeGetCurrent() - uploadBeginTS) forKey:@"duration"];
                map = tempDict;
            }
        } else {
            NSInteger code = obj.error.code;
            if (code == 0) {
                code = -1; // 未知错误
            }
            map = @{
                @"code" : @(code),
                @"identify" : asset.identifier ?: @""
            };
        }
        /// 单张图片的回调
        IMYURIActionBlockObject *uriActionObject = self.actionObject;
        imy_asyncMainExecuteBlock(^{
            [uriActionObject callbackWithObject:map error:nil eventName:@"album/selector/event"];
        });
    } allComplatedBlock:^(NSArray<id<IMYOSSFileObject>> *_Nonnull allObjects) {
        if (2 == self.pickCallbackType) {
            // 上传完成后，删除临时图片文件夹
            [[NSFileManager defaultManager] removeItemAtPath:dirPath error:nil];
            // 回调型，数据都回调完毕，直接释放 _actionObject 即可
            [self unholdSelf];
            return;
        }
        imy_asyncBlockExecuteBlock(^{
            NSMutableArray *results = [NSMutableArray array];
            NSInteger index = 0;
            for (id<IMYOSSFileObject> obj in allObjects) {
                if (0 == self.pickCallbackType) {
                    // 原始回调格式
                    NSString *urlString = [[obj url] absoluteString];
                    if ([obj state] == IMYOSSFileStateCompleted && urlString) {
                        [results addObject:urlString];
                    }
                } else {
                    // 新型回调
                    IMYAssetModel *asset = [assets imy_objectAtIndex:index];
                    NSString *urlString = [[obj url] absoluteString];
                    if ([obj state] == IMYOSSFileStateCompleted && urlString.length > 0) {
                        [results addObject:@{
                            @"code" : @0,
                            @"url" : urlString,
                            @"identify" : asset.identifier ?: @""
                        }];
                    } else {
                        @autoreleasepool {
                            NSInteger thumbSize = 300 / SCREEN_SCALE;
                            UIImage *thumb = [[asset thumbnailImageWithSize:CGSizeMake(thumbSize, thumbSize)] imy_fixOrientation];
                            NSString *thumbBase64 = [[thumb imy_toData] base64EncodedStringWithOptions:0];
                            NSInteger code = obj.error.code;
                            if (code == 0) {
                                code = -1; // 未知错误
                            }
                            [results addObject:@{
                                @"code" : @(code),
                                @"thumb" : thumbBase64 ?: @"",
                                @"identify" : asset.identifier ?: @""
                            }];
                        }
                    }
                }
                index ++;
            }
            [self pick_uploadImageSucessed:results];
            // 上传完成后，删除临时图片文件夹
            [[NSFileManager defaultManager] removeItemAtPath:dirPath error:nil];
        });
    }];
}

- (void)pick_uploadImageSucessed:(NSArray *)results {
    imy_asyncMainExecuteBlock(^{
        [UIView imy_hideHUD];
        // 协议结束
        [self runPickShowFinished:results];
    });
}

- (void)pick_selectPhotoWithAssets:(NSArray <IMYAssetModel *>*)assets {
    // show loading
    [UIView imy_showLoadingHUDWithText:@"上传中..."];
    
    // callback
    if (self.pickBase64) {
        // 采用 Base64 String 返回
        imy_asyncBlock(^{
            [self pick_new_uploadToBase64:assets];
        });
    } else {
        // 上传到CDN
        imy_asyncBlock(^{
            if (2 == self.pickCallbackType) {
                // 进度型回调
                [self pick_callback_uploadToCDN:assets];
            } else {
                // 完整型回调
                [self pick_new_uploadToCDN:assets];
            }
        });
    }
}

#pragma mark - UIImagePickerController

- (void)imagePickerController:(UIImagePickerController *)picker didFinishPickingMediaWithInfo:(NSDictionary *)info {
    // dismiss vc
    [picker dismissViewControllerAnimated:YES completion:nil];
    // pick vc dismiss
    [self runPickShowDismiss];
    // save image to photo
    UIImage *image = info[UIImagePickerControllerEditedImage] ?: info[UIImagePickerControllerOriginalImage];
    UIImage *normalizedImage = [image imy_normalizedImage];
    
    IMYAssetAuthorizationStatus status = [IMYAssetsManager authorizationStatusForAccessLevel:1];
    // 拍照完保存图片到相册需再获取相册权限
    if (status == IMYAssetAuthorizationStatusNotDetermined) {
        [PHPhotoLibrary requestAuthorization:^(PHAuthorizationStatus status) {
            if(status == PHAuthorizationStatusAuthorized) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    // 用户点击 "OK"
                    // 保存到相册中
                    [self savePhotoToAlbum:normalizedImage];
                });
            } else {
                dispatch_async(dispatch_get_main_queue(), ^{
                    // 用户点击 不允许访问
                    [UIView imy_showTextHUD:@"获取图片失败!"];
                    [self runPickShowFinished:nil];
                });
            }
        }];
    } else {
        // 保存到相册中
        [self savePhotoToAlbum:normalizedImage];
    }
}

-(void)savePhotoToAlbum:(UIImage *)image{
    // 保存到相册中
    [[IMYAssetsManager sharedInstance] saveImage:image
                               completionHandler:^(IMYAssetModel *_Nonnull model, NSError *_Nonnull error) {
        if (!error && model) {
            [self pick_selectPhotoWithAssets:@[model]];
        } else {
            [UIView imy_showTextHUD:@"获取图片失败!"];
            [self runPickShowFinished:nil];
        }
    }];
}

- (void)imagePickerControllerDidCancel:(UIImagePickerController *)picker {
    // dismiss vc
    [picker dismissViewControllerAnimated:YES completion:nil];
    // callback finished
    [self runPickShowDismiss];
    [self runPickShowFinished:nil];
}


#pragma mark - IMYAssetPickerController
/**
 选中图片回调，或者拍照成功回调
 
 @param assetPickerController assetPickerController description
 @param assets assets description
 */
- (void)assetPickerController:(IMYAssetPickerController *)assetPickerController didSelectAssets:(NSArray <IMYAssetModel *>*)assets {
    if (self.pickCrop && assets.count > 0) {
        // 有裁剪会走 assetPickerController:didCropImage: 回调
        return;
    }
    // pick结束
    [self runPickShowDismiss];
    if (!assets.count) {
        // 无选中照片
        [self runPickShowFinished:@[]];
        return;
    }
    [self pick_selectPhotoWithAssets:assets];
}

-(void)assetPickerController:(IMYAssetPickerController *)assetPickerController didCropImage:(UIImage *)image {
    // pick结束
    [self runPickShowDismiss];
    // 保存到相册中
    UIImage *normalizedImage = [image imy_normalizedImage];
    [[IMYAssetsManager sharedInstance] saveImage:normalizedImage
                               completionHandler:^(IMYAssetModel *_Nonnull model, NSError *_Nonnull error) {
        if (!error && model) {
            [self pick_selectPhotoWithAssets:@[model]];
        } else {
            [UIView imy_showTextHUD:@"获取图片失败!"];
            [self runPickShowFinished:nil];
        }
    }];
}

- (void)assetPickerControllerWillCancelling:(IMYAssetPickerController *)assetPickerController {
    // 用户取消选择
    [self runPickShowDismiss];
    [self runPickShowFinished:nil];
}


@end
