//
//  IMYPhotoBrowser.m
//  IMYViewKit
//
//  Created by <PERSON><PERSON>l<PERSON> on 15/6/3.
//  Copyright (c) 2015年 IMY. All rights reserved.
//

#import "IMYPhotoBrowser.h"
#import "IMYActionSheet.h"
#import "IMYImageSelectButton.h"
#import "IMYPhoto.h"
#import "IMYPhotoToolbar.h"
#import "IMYPhotoView.h"
#import "IMYViewKit.h"
#import "IMYCustomNavigationBarManager.h"
#import "IMYPhotoToAssetTransition.h"
#import "UIScrollView+IMYLanguages.h"
#import "IMYPhotoiCloudIndicatorView.h"
#import "IMYPhotoVideoActionBar.h"

#define kPadding 10
#define kPhotoViewTagOffset 1000
#define kPhotoViewIndex(photoView) ([photoView tag] - kPhotoViewTagOffset)
#define kVerticalGestureLimitOffSet 0
#define kCustomView_toolbar_y 30

UIKIT_EXTERN BOOL kIMYImagePhotoNavigationBarTranslucent;
NSString *const kIMYPhotoViewDismissNotification = @"kPhotoViewDismissNotification";
NSInteger const kIMYPhotoViewTag = 912857372;        //特殊值的tag，记得不要一样，否者后果自负
NSInteger const kIMYPhotoViewPresentTag = 912857373; //特殊值的tag，记得不要一样，否者后果自负

static UIWindow *staticWindow = nil;
static IMYPhotoBrowser *staticPhotoBrowser = nil;

@interface IMYPhotoMainScrollView : UIScrollView <UIGestureRecognizerDelegate>

@end


@interface IMYPhotoBrowser () <IMYPhotoViewDelegate, UINavigationControllerDelegate, UIGestureRecognizerDelegate> {
    // 所有的图片view
    NSMutableSet *_visiblePhotoViews;
    NSMutableSet *_reusablePhotoViews;

    // 一开始的状态栏
    BOOL _statusBarHiddenInited;
    BOOL _navBarTranslucentInited;
    UIStatusBarStyle _statusBarStyleInited;
    CGRect _customOriginRect;
}

@property (strong, nonatomic) UIScrollView *photoScrollView;
@property (strong, nonatomic) IMYImageSelectButton *btn_topRight;

@property (nonatomic) BOOL enableSwipeToDismiss; //IMYBrowserTypeNormal下，是否允许滑动返回上级页面，默认YES
@property (nonatomic, strong) UIView *coverView;
@property (nonatomic, strong) UIView *lastPushView;
@property (nonatomic, weak) UIView *footerView;

@property (nonatomic, assign) BOOL popVertical;
@property (nonatomic, assign) BOOL resetPanGestureDirection;// 用于手势最开始，仅判断一次是否为上下滑动手势
@property (nonatomic, assign) BOOL shouldCompletePopAnimation;// 手势结束是否需要直接dismiss整个大图浏览器
/// 7.9.7新样式，头部导航栏要黑色半透明
@property (nonatomic, strong) UIView *topBarView;
@property (nonatomic, strong) UILabel *topBarTitle;

@property (nonatomic, strong) IMYPhotoToAssetTransition *animatedTransition;

@property (nonatomic, strong) IMYPhotoiCloudIndicatorView *iCloudIndicatorView;

@property (nonatomic, strong) UIPanGestureRecognizer *panGesture;

@end

@implementation IMYPhotoBrowser

+ (instancetype)showWithPhoto:(id)photo {
    return [self showWithPhotos:[NSMutableArray arrayWithObject:photo]];
}

+ (instancetype)showWithPhotos:(NSMutableArray *)photos {
    return [self showWithPhotos:photos atIndex:0];
}

+ (instancetype)showWithPhotos:(NSMutableArray *)photos atIndex:(NSUInteger)index {
    return [self showWithPhotos:photos atIndex:index fromRect:CGRectZero];
}

+ (instancetype)showWithPhotos:(NSMutableArray *)photos isNeedShowCicrle:(BOOL)isNeedShowCicrle;
{
    return [self showWithPhotos:photos atIndex:0 fromRect:CGRectZero isNeedShowCicrle:isNeedShowCicrle];
}

+ (instancetype)showWithPhotos:(NSMutableArray *)photos atIndex:(NSUInteger)index fromRect:(CGRect)fromRect {
    return [self showWithPhotos:photos atIndex:index fromRect:fromRect isNeedShowCicrle:NO];
}

+ (instancetype)showWithPhotos:(NSMutableArray *)photos atIndex:(NSUInteger)index fromRect:(CGRect)fromRect delegate:(id)delegate {
    return [self showWithPhotos:photos atIndex:index fromRect:fromRect isNeedShowCicrle:NO];
}

+ (instancetype)showWithPhotos:(NSMutableArray *)photos
                       atIndex:(NSUInteger)index
                      fromRect:(CGRect)fromRect
              isNeedShowCicrle:(BOOL)isNeedShowCicrle
                      showType:(IMYBrowserType)showType
                      delegate:(id)delegate {
    if (staticWindow) {
#ifdef DEBUG
        [UIWindow imy_showTextHUD:@"异常：连续调用大图或者上个没释放"];
#endif
        return nil;
    }
    IMYPhotoBrowser *photoBrowser = [[IMYPhotoBrowser alloc] init];
    photoBrowser.delegate = delegate;
    photoBrowser.photos = photos;
    photoBrowser.needShowCicrle = isNeedShowCicrle;
    photoBrowser.currentPhotoIndex = index;
    photoBrowser.showType = showType;
    if (CGRectEqualToRect(CGRectZero, fromRect)) {
        [photoBrowser show];
    } else {
        [photoBrowser showWithAnimationAtIndex:index fromRect:fromRect];
    }
    return photoBrowser;
}

+ (instancetype)showWithPhotos:(NSMutableArray *)photos
                       atIndex:(NSUInteger)index
                      fromRect:(CGRect)fromRect
              isNeedShowCicrle:(BOOL)isNeedShowCicrle {
    return [self showWithPhotos:photos atIndex:index fromRect:fromRect isNeedShowCicrle:isNeedShowCicrle showType:IMYBrowserTypeNormal delegate:nil];
}

#pragma mark - Lifecycle

- (id)initWithNibName:(NSString *)nibNameOrNil bundle:(NSBundle *)nibBundleOrNil {
    self = [super initWithNibName:nibNameOrNil bundle:nibBundleOrNil];
    if (self) {
        /// 普通模式下，才默认 YES
        _enableSwipeToDismiss = NO;
        _currentPhotoIndex = -1;
        _showSave = YES;
        _chooseText = IMYString(@"完成");
        _allowSelectionVideo = NO;
        _currentSelectionType = IMYAssetTypeNone;
    }
    return self;
}
- (void)dealloc {
    _photoScrollView.delegate = nil;
}

- (BOOL)isNavigationBarHidden {
    return YES;
}

- (id)init {
    self = [super init];
    if (self) {
        _currentPhotoIndex = -1;
        self.wantsFullScreenLayout = YES;
        if (IOS7) {
            self.enableIOS7EdgesForExtendedLayout = YES;
            self.extendedLayoutIncludesOpaqueBars = YES;
            self.automaticallyAdjustsScrollViewInsets = NO;
        }
    }
    return self;
}

- (void)loadView {
    [super loadView];
    _statusBarHiddenInited = [UIApplication sharedApplication].isStatusBarHidden;
    _navBarTranslucentInited = self.navigationController.navigationBar.translucent;
    _statusBarStyleInited = [[UIApplication sharedApplication] statusBarStyle];
//    if (self.navigationController.viewControllers.count <= 1) {
        //非多选照片
        // 隐藏状态栏
        imy_asyncMainBlock(0.25, ^{
            [[UIApplication sharedApplication] setStatusBarHidden:YES withAnimation:UIStatusBarAnimationNone];
        });
//    }

    [self imy_topLeftButtonIsBack];
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    [[UIApplication sharedApplication] setStatusBarHidden:_statusBarHiddenInited withAnimation:NO];
    self.navigationController.navigationBar.translucent = _navBarTranslucentInited;
    [[UIApplication sharedApplication] setStatusBarStyle:_statusBarStyleInited];
}

- (void)viewDidDisappear:(BOOL)animated {
    [super viewDidDisappear:animated];
    if (self.completeBlock) {
        self.completeBlock();
    }
}
- (void)viewDidLoad {
    if (self.showType == IMYBrowserTypeNormal || self.showType == IMYBrowserTypeCustomView || self.showType == IMYBrowserTypeFeedsOversea) {
        self.enableSwipeToDismiss = YES;
    }

    [super viewDidLoad];
//    [[UIApplication sharedApplication] setStatusBarStyle:UIStatusBarStyleLightContent animated:YES];
    // TODO: kIMYImagePhotoNavigationBarTranslucent 在IMYImagepickerController中赋值为NO后没有其他地方改变赋值, 这里先直接使用NO
    self.navigationController.navigationBar.translucent = NO; //kIMYImagePhotoNavigationBarTranslucent;
    [self.view imy_removeThemeActionBlockForKey:@"setBackgroundColor"];
    self.view.backgroundColor = [UIColor blackColor];
    // 2.创建工具条
    if (self.showType == IMYBrowserTypeAlbum) {
        [self createMultipToolbar];
        [self updateTopRightBtn];
        [self.view performSelector:@selector(bringSubviewToFront:) withObject:_multipleToolbar afterDelay:0];
        [self setupTopBarView:NO];
        [self.view performSelector:@selector(bringSubviewToFront:) withObject:self.topBarView afterDelay:0.02];
        /// 开始下拉退出手势
        ((IMYPublicBaseNavigationController *)self.navigationController).wrapDelegate = self;
        /// 这里是因为IMYPublicBaseNavigationController (IMYCustomTransition)做了拦截，下面属性设置为YES,才会走自己的协议
        [IMYCustomNavigationBarManager sharedManager].isIgnore = YES;
        self.animatedTransition = [IMYPhotoToAssetTransition new];
        self.enableSwipeToDismiss = YES;
        [self setupPanGestureRecognizer];
    } else if (self.showType == IMYBrowserTypeNormal || self.showType == IMYBrowserTypeCustomView || self.showType == IMYBrowserTypeFeedsOversea) {
        if (self.enableSwipeToDismiss) {
            self.coverView = [[UIView alloc] initWithFrame:self.view.bounds];
            self.coverView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
            [self.view addSubview:self.coverView];

            self.coverView.backgroundColor = [UIColor colorWithWhite:0 alpha:0];
            [UIView animateWithDuration:0.25
                             animations:^{
                                 self.coverView.backgroundColor = [UIColor colorWithWhite:0 alpha:1];
                             }];
        }

        // 1.创建UIScrollView
        [self createScrollView];
        [self setupPanGestureRecognizer];
        [self createToolbar];
    } else if (self.showType == IMYBrowserTypePublish) {
        [self setupTopBarView:YES];
    }
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    if (self.showType == IMYBrowserTypeAlbum || self.showType == IMYBrowserTypePublish) {
        // 1.创建UIScrollView
        if (!self.photoScrollView) {
            @weakify(self);
            imy_asyncMainBlock(^{
                @strongify(self);
                if (!self.photoScrollView) {
                    [self createScrollView];
                    [self setupPanGestureRecognizer];
                    [self updateTollbarState];
                    [self showPhotos];
                    [self.view bringSubviewToFront:self.multipleToolbar];
                    [self.view bringSubviewToFront:self.topBarView];
                }
            });
        }
    }
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    [self disableIOS7PopGestureRecongnizer];
}

- (void)rightButtonAction:(IMYImageSelectButton *)sender {
    // 照片和视频互斥
    if (!sender.selected && self.allowSelectionVideo && IMYAssetTypeNone != self.currentSelectionType) {
        IMYPhoto *photo = self.photos[_currentPhotoIndex];
        if (photo.assetModel && photo.assetModel.assetType != self.currentSelectionType) {
            [UIView imy_showTextHUD:IMYString(@"不能同时选择图片和视频")];
            return;
        }
    }
    // 判断是否超过最大值
    if (!sender.selected && [self isSelectedMaximum]) {
        NSString *typeName = IMYString(@"图片");
        NSString *unitName = IMYString(@"张");
        NSUInteger maxCount = self.maxPhotos;
        if (self.currentSelectionType == IMYAssetTypeVideo) {
            if (self.maxVideoSelectCount > 0) {
                maxCount = self.maxVideoSelectCount;
            }
            typeName = IMYString(@"视频");
            unitName = IMYString(@"个");
        }
        NSString *toast = [NSString stringWithFormat:IMYString(@"最多能选%d%@%@"), maxCount, unitName, typeName];
        [UIView imy_showTextHUD:toast];
        return;
    }
    
    sender.selected = !sender.selected;

    IMYPhoto *photo = self.photos[_currentPhotoIndex];
    if (photo.assetModel) {
        if (sender.selected) {
            [sender startAnimation];
            [self.selectedAssetUrls addObject:photo.assetModel];
        } else {
            if ([self.selectedAssetUrls containsObject:photo.assetModel]) {
                [self.selectedAssetUrls removeObject:photo.assetModel];
            }
        }
        // 视频和照片互斥
        if (self.allowSelectionVideo) {
            if (self.selectedAssetUrls.count == 0) {
                self.currentSelectionType = IMYAssetTypeNone;
            } else {
                self.currentSelectionType = photo.assetModel.assetType;
            }
        }
    } else {
        if (sender.selected) {
            [sender startAnimation];
            [self.selectedAssetUrls addObject:photo.albumUrl];
        } else {
            for (NSURL *selectUrl in self.selectedAssetUrls) {
                if ([selectUrl.absoluteString isEqualToString:photo.albumUrl.absoluteString]) {
                    [self.selectedAssetUrls removeObject:selectUrl];
                    break;
                }
            }
        }
    }

    if (self.selectedAssetUrls.count) {
        self.btn_done.enabled = YES;
        [self.btn_done setTitle:[NSString stringWithFormat:@"%@(%@)", self.chooseText, @(self.selectedAssetUrls.count)] forState:UIControlStateNormal];
    } else {
        self.btn_done.enabled = NO;
        [self.btn_done setTitle:self.chooseText forState:UIControlStateNormal];
    }
}

- (void)deleteImage:(id)sender {
    @weakify(self);
    [IMYActionSheet sheetWithCancelTitle:IMYString(@"取消")
                             otherTitles:@[IMYString(@"删除")]
                                 summary:IMYString(@"要删除这张照片吗？")
                              showInView:self.navigationController.view
                                  action:^(NSInteger index) {
                                      if (index == 1) {
                                          @strongify(self);
                                          if (self.deleteBlock && self.photos.count > _currentPhotoIndex) {
                                              if (self.photos.count == 1) {
                                                  if ([self.delegate respondsToSelector:@selector(photoBrowserWillHide:)]) {
                                                      [self.delegate photoBrowserWillHide:self];
                                                  }
                                                  [[UIApplication sharedApplication] setStatusBarHidden:NO withAnimation:UIStatusBarAnimationNone];
                                                  [UIView animateWithDuration:0.25
                                                      animations:^{
                                                      }
                                                      completion:^(BOOL finished) {
                                                          [self imy_pop:YES];
                                                      }];
                                              } else {
                                                  [self.photos removeObjectAtIndex:_currentPhotoIndex];

                                                  [self performSelectorOnMainThread:@selector(reloadData) withObject:nil waitUntilDone:NO modes:@[NSRunLoopCommonModes]];
                                              }
                                              self.deleteBlock(_currentPhotoIndex);
                                          }
                                      }
                                  }];
}


- (void)show {
    if (self.enableSwipeToDismiss) {
    }

    staticWindow.hidden = YES;
    if (!staticWindow) {
        staticWindow = [[IMYNormalWindow alloc] initWithFrame:[UIScreen mainScreen].bounds];
        [staticWindow addSubview:self.view];
    }
    self.view.hidden = NO;
    self.view.backgroundColor = [UIColor clearColor];
    staticWindow.windowLevel = UIWindowLevelStatusBar - 1;
    staticWindow.hidden = NO;
    staticPhotoBrowser = self;
    [staticWindow makeKeyAndVisible];

    self.view.tag = kIMYPhotoViewTag; //特殊值的tag，记得不要一样，否者后果自负

    if (_currentPhotoIndex == 0) {
        [self showPhotos];
    }
    if (isSimulator) {
        IMYPhoto *photo = self.photos.firstObject;
        if ([photo.url.absoluteString.lowercaseString containsString:@"android"]) {
            [UIView imy_showTextHUD:@"image from android device"];
        } else if ([photo.url.absoluteString.lowercaseString containsString:@"ios"]) {
            [UIView imy_showTextHUD:@"image from ios device"];
        }
    }
    //代理回调 自定义界面
    if ([self.delegate respondsToSelector:@selector(photoBrowseCustomFooterView:)]) {
        self.footerView = [self.delegate photoBrowseCustomFooterView:self];
        [self.view addSubview:self.footerView];
        [self.view bringSubviewToFront:self.footerView];
    }
}

- (void)dismiss {
    if ([self.delegate respondsToSelector:@selector(photoBrowserWillHide:)]) {
        [self.delegate photoBrowserWillHide:self];
    }
    [[UIApplication sharedApplication] setStatusBarHidden:_statusBarHiddenInited withAnimation:UIStatusBarAnimationNone];
    self.view.backgroundColor = [UIColor clearColor];
    // 移除工具条
    [_toolbar removeFromSuperview];
    [self.view removeFromSuperview];
    [self removeFromParentViewController];
    [self cleanStaticWindow];
}

- (void)showWithAnimationAtIndex:(NSUInteger)index fromRect:(CGRect)fromRect {
    UIImageView *presentImageView = [[UIImageView alloc] initWithFrame:fromRect];
    presentImageView.contentMode = UIViewContentModeScaleAspectFit;
    IMYPhoto *photo = self.photos[index];
    if (photo.sy_placeholder) {
        presentImageView.image = photo.sy_placeholder;
    } else if (photo.url) {
        [presentImageView imy_setImageURL:photo.url];
    } else if (photo.assetModel) {
        presentImageView.image = [photo.assetModel thumbnailImageWithSize:CGSizeMake(SCREEN_WIDTH, SCREEN_WIDTH)];
    }
    presentImageView.tag = kIMYPhotoViewPresentTag;
    photo.srcImageView = presentImageView;
    
    staticWindow = [[IMYNormalWindow alloc] initWithFrame:[UIScreen mainScreen].bounds];
    staticWindow.windowLevel = UIWindowLevelStatusBar - 1;
    staticWindow.hidden = NO;
    [staticWindow addSubview:presentImageView];
    [staticWindow addSubview:self.view];
    self.view.hidden = YES;

    [self show];
    presentImageView.hidden = YES;
}

- (void)setDeleteBlock:(BKIndexBlock)deleteBlock {
    _deleteBlock = deleteBlock;
    _toolbar.showDeleteButton = YES;
}

- (void)msgTouch:(NSNotification *)notification {
    [self dismiss];
}

- (void)setPageControlStyle:(IMYBrowserPageControlStyle)pageControlStyle {
    _pageControlStyle = pageControlStyle;
    self.toolbar.pageControlStyle = pageControlStyle;
    self.toolbar.currentPhotoIndex = self.currentPhotoIndex;
}

#pragma mark - 私有方法
#pragma mark 创建工具条
- (void)createToolbar {
    if(self.showType == IMYBrowserTypeNormal && self.allowSelectionVideo){
        return; // 视频预览一条全屏，不创建底部pagecontrol
    }
    CGFloat barHeight = 44;
    CGFloat barY = self.view.frame.size.height - barHeight - SCREEN_TABBAR_SAFEBOTTOM_MARGIN;
    _toolbar = [[IMYPhotoToolbar alloc] init];
    _toolbar.pageControlStyle = self.pageControlStyle;
    _toolbar.frame = CGRectMake(0, barY, self.view.frame.size.width, barHeight);
    _toolbar.autoresizingMask = UIViewAutoresizingFlexibleTopMargin;
    _toolbar.currentPhotoIndex = _currentPhotoIndex;
    _toolbar.photos = self.photos;
    _toolbar.controller = self;
    if (self.showType == IMYBrowserTypeCustomView) {
        _toolbar.indexLabel.textAlignment = NSTextAlignmentLeft;
        _toolbar.imy_top = kCustomView_toolbar_y;
        _toolbar.imy_left = 16;
        _toolbar.indexLabel.font = [UIFont systemFontOfSize:16 weight:UIFontWeightRegular];
    } else if (self.showType == IMYBrowserTypeFeedsOversea){
        _toolbar.indexLabel.textAlignment = NSTextAlignmentCenter;
        _toolbar.imy_top = SCREEN_STATUSBAR_HEIGHT;
        _toolbar.imy_left = 0;
        _toolbar.indexLabel.font = [UIFont systemFontOfSize:17 weight:UIFontWeightRegular];
        _toolbar.pageControlStyle = IMYBrowserPageControlStyleText;
        _toolbar.showCloseButton = YES;
        [_toolbar.closeBtn addTarget:self action:@selector(dismiss) forControlEvents:UIControlEventTouchUpInside];
    }
    @weakify(self);
    _toolbar.deleteBlock = ^(NSUInteger index) {
        @strongify(self);
        if (self.deleteBlock) {
            if (self.photos.count == 1) {
                if ([self.delegate respondsToSelector:@selector(photoBrowserWillHide:)]) {
                    [self.delegate photoBrowserWillHide:self];
                }
                [[UIApplication sharedApplication] setStatusBarHidden:NO withAnimation:UIStatusBarAnimationNone];
                [UIView animateWithDuration:0.25
                    animations:^{
                        self.view.alpha = 0;
                    }
                    completion:^(BOOL finished) {
                        [self hide];
                    }];
            } else {
                self.currentPhotoIndex = MAX((int)index - 1, 0);
                self.toolbar.pageControl.numberOfPages -= 1;
                self.toolbar.pageControl.currentPage = self.currentPhotoIndex;
                [self.photos removeObjectAtIndex:index];

                [self performSelectorOnMainThread:@selector(reloadData) withObject:nil waitUntilDone:NO modes:@[NSRunLoopCommonModes]];
            }
            self.deleteBlock(index);
        }
    };
    [self.view addSubview:_toolbar];

    [self updateTollbarState];
}


#pragma mark 创建UIScrollView
- (void)createScrollView {
    CGRect frame = self.view.bounds;
    frame.origin.x -= kPadding;
    frame.size.width += (2 * kPadding);
    
    self.photoScrollView = [[IMYPhotoMainScrollView alloc] initWithFrame:frame];;
    _photoScrollView.delegate = self;
    _photoScrollView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
    _photoScrollView.pagingEnabled = YES;
    _photoScrollView.showsHorizontalScrollIndicator = NO;
    _photoScrollView.showsVerticalScrollIndicator = NO;
    _photoScrollView.backgroundColor = [UIColor clearColor];
    _photoScrollView.contentSize = CGSizeMake(frame.size.width * self.photos.count, 0);
    if (IMYISRTL) {
        [_photoScrollView imy_enableAutoRTL];
    }
    if (self.photos.count == 1 && self.showType == IMYBrowserTypeNormal) {
        //这里减少0.5宽度以便在单图情况下也有弹性效果
        _photoScrollView.imy_width -= 0.5;
    }
    _photoScrollView.contentOffset = CGPointMake(_currentPhotoIndex * frame.size.width, 0);
    [self.view addSubview:_photoScrollView];
}

- (void)setupPanGestureRecognizer {
    if (self.enableSwipeToDismiss && !self.panGesture) {
        self.panGesture = [[UIPanGestureRecognizer alloc] initWithTarget:self action:@selector(pan:)];
        self.panGesture.delegate = self;
        [self.view addGestureRecognizer:self.panGesture];
    }
}

- (void)setPhotos:(NSMutableArray *)photos {
    _photos = photos;
    if (photos.count > 1) {
        _visiblePhotoViews = [NSMutableSet set];
        _reusablePhotoViews = [NSMutableSet set];
    }

    for (int i = 0; i < _photos.count; i++) {
        IMYPhoto *photo = _photos[i];
        photo.index = i;
        photo.firstShow = (i == _currentPhotoIndex);
    }
}

#pragma mark 设置选中的图片
- (void)setCurrentPhotoIndex:(NSInteger)currentPhotoIndex {
    _currentPhotoIndex = currentPhotoIndex;

    for (int i = 0; i < _photos.count; i++) {
        IMYPhoto *photo = _photos[i];
        photo.firstShow = i == currentPhotoIndex;
    }

    if ([self isViewLoaded]) {
        _photoScrollView.contentOffset = CGPointMake(_currentPhotoIndex * _photoScrollView.frame.size.width, 0);

        // 显示所有的相片
        [self showPhotos];
    }
}

- (void)hide {
    [self photoViewSingleTap:nil];
    [self photoViewDidEndZoom:nil];
    IMY_POST_NOTIFY(kIMYPhotoViewDismissNotification);
}

#pragma mark - IMYPhotoView代理
- (BOOL)photoViewHideAnimationUseFade {
    IMY_POST_NOTIFY(kIMYPhotoViewDismissNotification);
    if ([self.delegate respondsToSelector:@selector(photoBrowserWillHide:)]) {
        [self.delegate photoBrowserWillHide:self];
    }
    return _hideUseFadeAnimation;
}
- (void)photoViewSingleTap:(IMYPhotoView *)photoView {
    if (self.showType == IMYBrowserTypeAlbum || self.showType == IMYBrowserTypePublish) {
        //多选照片
        if (self.topBarView.hidden) {
            self.topBarView.hidden = NO;
            self.multipleToolbar.hidden = NO;
        } else {
            self.topBarView.hidden = YES;
            self.multipleToolbar.hidden = YES;
        }
    } else {
        [[UIApplication sharedApplication] setStatusBarHidden:_statusBarHiddenInited withAnimation:UIStatusBarAnimationNone];
        self.view.backgroundColor = [UIColor clearColor];
        // 移除工具条
        [_toolbar removeFromSuperview];
        /// 图片点击触发的，直接使用手势的消失动画
        if (photoView) {
            [self verticalPanGestureEnd];
        }
    }
}

- (void)cleanStaticWindow {
    staticPhotoBrowser = nil;
    staticWindow.hidden = YES;
    staticWindow = nil;
    imy_asyncMainBlock(^{
        NSArray<UIWindow *> *windowArray = [UIApplication sharedApplication].windows;
        for (UIWindow *window in windowArray) {
            NSString *className = NSStringFromClass(window.class);
            // UITextEffectsWindow，如果不对他执行 makeKeyWindow ，UIMenuController 会一直弹不出来
            if ([className containsString:@"TextEffects"]) {
                [window makeKeyWindow];
                break;
            }
        }
        imy_asyncMainBlock(^{
            // 焦点 再回归到 真正的 keyWindow
            [[UIApplication sharedApplication].delegate.window makeKeyWindow];
        });
    });
}

- (void)photoViewDidEndZoom:(IMYPhotoView *)photoView {
    [self.view removeFromSuperview];
    [self removeFromParentViewController];
    [self cleanStaticWindow];
}

- (void)photoViewImageFinishLoad:(IMYPhotoView *)photoView {
    if ([self.delegate respondsToSelector:@selector(photoBrowser:didChangedToPageAtIndex:)]) {
        [self.delegate photoBrowser:self didChangedToPageAtIndex:_currentPhotoIndex];
    }
    if ([self.delegate respondsToSelector:@selector(photoBrowser:didFinishedLoad:)]) {
        NSInteger index = [self.photos indexOfObject:photoView.photo];
        [self.delegate photoBrowser:self didFinishedLoad:index];
    }
    _toolbar.currentPhotoIndex = _currentPhotoIndex;
    IMYPhoto *currentPhoto = self.photos[self.currentPhotoIndex];
    
    BOOL isGif = currentPhoto.assetModel.assetType == IMYAssetTypeImage && currentPhoto.assetModel.assetSubType == IMYAssetSubTypeGIF;
    if (self.showEdit) {
        self.editBtn.hidden = isGif;
    }
    if ([photoView.photo.assetModel.identifier isEqualToString:currentPhoto.assetModel.identifier]) {
        if ([self.selectedAssetUrls containsObject:currentPhoto.assetModel]) {
            self.editBtn.enabled = YES;
        }else {//照片未选中，已选图片达到30张，编辑按钮置灰不可点击
            self.editBtn.enabled = ![self isSelectedMaximum];
        }
        
    }
    
    [self showiCloudIndicatorWithAssetModel:currentPhoto.assetModel];
}

- (void)photoViewImageFail:(IMYPhotoView *)photoView {
    _toolbar.saveImageBtn.enabled = NO;
    _toolbar.deleteImageBtn.enabled = YES;
}

- (void)photoViewShouldLongPress:(IMYPhotoView *)photoView {
    if (self.showType != IMYBrowserTypeNormal && self.showType != IMYBrowserTypeCustomView) {
        return;
    }
    if (self.allowSelectionVideo && photoView.photo.assetModel.assetType == IMYAssetTypeVideo) {
        // 视频预览模式下，不允许保存照片
        return;
    }
    if (((_toolbar.saveImageBtn.enabled && _showSave) || _showDelete) && ![self.view imy_findSubviewWithClass:[IMYActionSheet class]]) {
        @weakify(self);
        NSMutableArray *titles = [NSMutableArray array];
        if (_showSave) {
            [titles addObject:IMYString(@"保存图片")];
        }
        if (_showDelete) {
            [titles addObject:IMYString(@"删除")];
        }
        [IMYActionSheet sheetWithCancelTitle:IMYString(@"取消")
                                 otherTitles:titles
                                     summary:nil
                                  showInView:self.view
                                      action:^(NSInteger index) {
                                          @strongify(self);
                                          if (index == 1) {
                                              [self.toolbar saveImage];
                                              if ([self.delegate respondsToSelector:@selector(photoBrowserWillSavePhoto:)]) {
                                                  [self.delegate photoBrowserWillSavePhoto:self];
                                              }
                                          } else if (index == 2) {
                                              [self.toolbar deleteImage];
                                          }
                                      }];
    }
}


- (void)reloadData {
    [_visiblePhotoViews makeObjectsPerformSelector:@selector(removeFromSuperview)];
    [_visiblePhotoViews removeAllObjects];
    self.photoScrollView.contentSize = CGSizeMake((SCREEN_WIDTH + 20) * self.photos.count, 0);
    [self showPhotoViewAtIndex:self.currentPhotoIndex];
    [self updateTollbarState];
}

#pragma mark 显示照片
- (void)showPhotos {
    // 只有一张图片
    if (self.photos.count == 1) {
        [self showPhotoViewAtIndex:0];
        return;
    }

    CGRect visibleBounds = _photoScrollView.bounds;
    NSInteger firstIndex = (NSInteger)floorf((CGRectGetMinX(visibleBounds) + kPadding * 2) / CGRectGetWidth(visibleBounds));
    NSInteger lastIndex = (NSInteger)floorf((CGRectGetMaxX(visibleBounds) - kPadding * 2 - 1) / CGRectGetWidth(visibleBounds));
    if (firstIndex < 0)
        firstIndex = 0;
    if (firstIndex >= self.photos.count)
        firstIndex = self.photos.count - 1;
    if (lastIndex < 0)
        lastIndex = 0;
    if (lastIndex >= self.photos.count) {
        lastIndex = self.photos.count - 1;
    }

    // 回收不再显示的ImageView
    NSInteger photoViewIndex;
    for (IMYPhotoView *photoView in _visiblePhotoViews) {
        photoViewIndex = kPhotoViewIndex(photoView);
        if (photoViewIndex < firstIndex || photoViewIndex > lastIndex) {
            [_reusablePhotoViews addObject:photoView];
            [photoView removeFromSuperview];
        }
    }

    [_visiblePhotoViews minusSet:_reusablePhotoViews];
    while (_reusablePhotoViews.count > 2) {
        [_reusablePhotoViews removeObject:[_reusablePhotoViews anyObject]];
    }

    for (NSUInteger index = firstIndex; index <= lastIndex; index++) {
        if (![self isShowingPhotoViewAtIndex:index]) {
            [self showPhotoViewAtIndex:index];
        }
    }
}

#pragma mark 显示一个图片view

- (void)showPhotoViewAtIndex:(NSInteger)index {
//    if (self.photos.count == 1 && _photoScrollView.subviews.count > 0) {
//        return;
//    }
    IMYPhotoView *photoView;
    if (self.photos.count == 1 && _photoScrollView.subviews.count > 0) {
        if (self.showType != IMYBrowserTypeAlbum) {
            return;
        }
        photoView = [self currentPhotoView];
    } else {
        photoView = [self dequeueReusablePhotoView];
    }
    
    if (!photoView) { // 添加新的图片view
        photoView = [[IMYPhotoView alloc] init];
        photoView.needShowCicrle = self.needShowCicrle;
        photoView.photoViewDelegate = self;
        if (self.showType == IMYBrowserTypeNormal && self.allowSelectionVideo) {
            photoView.showFullScreenVideo = YES;
            photoView.autoPlayVideo = self.autoPlayVideo;
        }
        if (self.showType == IMYBrowserTypeFeedsOversea) {
            photoView.overseaFailLoad = YES;
        }
    }
    photoView.allowSelectionVideo = self.allowSelectionVideo;
    photoView.ignoreDidEndZoomEvent = self.ignoreDidEndZoomEvent;

    // 调整当期页的frame
    CGRect bounds = _photoScrollView.bounds;
    CGRect photoViewFrame = bounds;
    photoViewFrame.size.width -= (2 * kPadding);
    photoViewFrame.origin.x = (bounds.size.width * index) + kPadding;
    photoView.tag = kPhotoViewTagOffset + index;

    IMYPhoto *photo = self.photos[index];
    photoView.frame = photoViewFrame;
    photoView.photo = photo;

    [_visiblePhotoViews addObject:photoView];

    // 是不是可以理解为这里的photoView是当前显示的图片？
    [_photoScrollView addSubview:photoView];
    
    if (index == self.currentPhotoIndex) {
        BOOL isGif = photo.assetModel.assetType == IMYAssetTypeImage && photo.assetModel.assetSubType == IMYAssetSubTypeGIF;
        if (self.showEdit) {
            self.editBtn.hidden = isGif;
        }
        if ([self.selectedAssetUrls containsObject:photo.assetModel]) {
            self.editBtn.enabled = (photo.image != nil);
        }else {//照片未选中，已选图片达到30张，编辑按钮置灰不可点击；未达到30张，图片加载完毕可点，图片未加载完毕不可点
            self.editBtn.enabled = (photo.image != nil) && ![self isSelectedMaximum];
        }
    }

    [self loadImageNearIndex:index];
}

#pragma mark 加载index附近的图片

- (void)loadImageNearIndex:(NSInteger)index {
    if (self.showType == IMYBrowserTypeAlbum || self.showType == IMYBrowserTypePublish) {
        return;
    }
    if (index > 0) {// 下载前一张图片
        IMYPhoto *photo = self.photos[index - 1];
        if (photo.url) {
            [[SDWebImageManager sharedManager] prefetchImageWithURL:photo.url];
        }
    }

    if (index < self.photos.count - 1) {// 下载后一张图片
        IMYPhoto *photo = self.photos[index + 1];
        if (photo.url) {
            [[SDWebImageManager sharedManager] prefetchImageWithURL:photo.url];
        }
    }
}

#pragma mark index这页是否正在显示
- (BOOL)isShowingPhotoViewAtIndex:(NSUInteger)index {
    for (IMYPhotoView *photoView in _visiblePhotoViews) {
        if (kPhotoViewIndex(photoView) == index) {
            return YES;
        }
    }
    return NO;
}

- (IMYPhotoView *)currentPhotoView {
    IMYPhotoView *curPhotoView = nil;
    if (self.photos.count == 1) {
        curPhotoView = self.photoScrollView.subviews.firstObject;
    } else {
        for (IMYPhotoView *photoView in _visiblePhotoViews) {
            if (kPhotoViewIndex(photoView) == self.currentPhotoIndex) {
                curPhotoView = photoView;
                break;
            }
        }
    }
    return curPhotoView;
}

#pragma mark 循环利用某个view
- (IMYPhotoView *)dequeueReusablePhotoView {
    IMYPhotoView *photoView = [_reusablePhotoViews anyObject];
    if (photoView) {
        [_reusablePhotoViews removeObject:photoView];
    }
    return photoView;
}

#pragma mark 更新toolbar状态
- (void)updateTollbarState {
    NSInteger index = (_photoScrollView.contentOffset.x + 160) / _photoScrollView.frame.size.width;
    if (_currentPhotoIndex != index) {
        _currentPhotoIndex = index;
        if ([self.delegate respondsToSelector:@selector(photoBrowser:didChangedToPageAtIndex:)]) {
            [self.delegate photoBrowser:self didChangedToPageAtIndex:index];
        }
        _toolbar.currentPhotoIndex = _currentPhotoIndex;
    }
    if (self.showType == IMYBrowserTypeAlbum) {
        [self updateTopRightBtn];
    } else if (self.showType == IMYBrowserTypePublish) {
        self.topBarTitle.text = [NSString stringWithFormat:@"%ld/%lu", (long)_currentPhotoIndex + 1, (unsigned long)self.photos.count];
    }
    
    IMYPhoto *currentPhoto = self.photos[self.currentPhotoIndex];
    [self showiCloudIndicatorWithAssetModel:currentPhoto.assetModel];
}

#pragma mark - UIScrollView Delegate
- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    [self showPhotos];
    [self updateTollbarState];
    IMYPhoto *currentPhoto = self.photos[self.currentPhotoIndex];
    
    BOOL isGif = currentPhoto.assetModel.assetType == IMYAssetTypeImage && currentPhoto.assetModel.assetSubType == IMYAssetSubTypeGIF;
    if (self.showEdit) {
        self.editBtn.hidden = isGif;
    }
    if ([self.selectedAssetUrls containsObject:currentPhoto.assetModel]) {
//        self.editBtn.enabled = YES;
        self.editBtn.enabled = (currentPhoto.image != nil);
    }else {//照片未选中，已选图片达到30张，编辑按钮置灰不可点击；未达到30张，图片加载完毕可点，图片未加载完毕不可点
        self.editBtn.enabled = (currentPhoto.image != nil) && ![self isSelectedMaximum];
    }
}

/// 已到达选择数量上限
- (BOOL)isSelectedMaximum {
    NSUInteger maxCount = self.maxPhotos;
    if (self.currentSelectionType == IMYAssetTypeVideo && self.maxVideoSelectCount > 0) {
        maxCount = self.maxVideoSelectCount;
    }
    return self.selectedAssetUrls.count >= maxCount;
}

- (void)scrollViewWillBeginDragging:(UIScrollView *)scrollView {
//    if (self.showType != IMYBrowserTypeNormal && self.showType != IMYBrowserTypeCustomView) {
//        self.topBarView.hidden = YES;
//        self.multipleToolbar.hidden = YES;
//    }
}
- (void)scrollViewDidEndDragging:(UIScrollView *)scrollView willDecelerate:(BOOL)decelerate {
    if (self.showType == IMYBrowserTypeCustomView) {
        CGRect visibleBounds = _photoScrollView.bounds;
        NSInteger lastIndex = (NSInteger)floorf((CGRectGetMaxX(visibleBounds) - kPadding * 2 - 1) / CGRectGetWidth(visibleBounds));
        if (lastIndex >= self.photos.count) {
            [UIView imy_showTextHUD:IMYString(@"最后一张图片~")];
        }
    }
}
#pragma mark - 多选状态下的toolbar
/// 创建topbar， isDelete决定样式
- (void)setupTopBarView:(BOOL)isDelete {
    self.topBarView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT)];
    self.topBarView.backgroundColor = [UIColor colorWithWhite:0 alpha:0.85];
    [self.view addSubview:self.topBarView];
    /// 返回按钮
    UIButton *backButton = [[UIButton alloc] initWithFrame:CGRectMake(10, SCREEN_STATUSBAR_HEIGHT, 44, 44)];
    [backButton imy_setImage:@"nav_btn_back"];
    
    [backButton imy_addLanguageChangedActionBlock:^(UIButton *weakObject) {
        UIImage *img = [UIImage imy_imageForKey:@"nav_btn_back"];
        if (IMYISRTL) {
            img = [img imageWithHorizontallyFlippedOrientation];
            weakObject.imy_right = SCREEN_WIDTH - 10;
        } else {
            weakObject.imy_left = 10;
        }
        [weakObject imy_setImage:img];
    } forKey:@"setImage"];
    
    @weakify(self);
    [[backButton rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(__kindof UIControl * _Nullable x) {
        @strongify(self);
        [self imy_pop:YES];
    }];
    [self.topBarView addSubview:backButton];
    if (isDelete) {
        IMYTouchEXButton *button = [[IMYTouchEXButton alloc] initWithFrame:CGRectMake(SCREEN_WIDTH - 44 - 10, SCREEN_STATUSBAR_HEIGHT, 44, 44)];
        [button setImage:[UIImage imy_imageForKey:@"album_choose_del"] forState:UIControlStateNormal];
        [button addTarget:self action:@selector(deleteImage:) forControlEvents:UIControlEventTouchUpInside];
        [button setImageEdgeInsets:UIEdgeInsetsMake(0, 3, 0, 0)];
        [self.topBarView addSubview:button];
        
        [button imy_addLanguageChangedActionBlock:^(IMYTouchEXButton *weakObject) {
            if (IMYISRTL) {
                weakObject.imy_left = 10;
            } else {
                weakObject.imy_right = SCREEN_WIDTH - 10;
            }
        } forKey:@"setPosition"];
        
        UILabel *label = [[UILabel alloc] initWithFrame:CGRectMake(0, SCREEN_STATUSBAR_HEIGHT, 100, 44)];
        label.imy_centerX = SCREEN_WIDTH/2;
        label.textAlignment = NSTextAlignmentCenter;
        label.font = [UIFont boldSystemFontOfSize:17];
        label.textColor = [UIColor whiteColor];
        [self.topBarView addSubview:label];
        self.topBarTitle = label;

    } else {
        if (IMYPublicAppHelper.isWenzhenYisheng) {
            _btn_topRight = [[IMYImageSelectButton alloc] initWithFrame:CGRectMake(SCREEN_WIDTH - 44 - 10, SCREEN_STATUSBAR_HEIGHT + 8, 28, 28) normalImage:@"image_chose" selectedImage:@"ys_chose_up"];
        } else
            _btn_topRight = [[IMYImageSelectButton alloc] initWithFrame:CGRectMake(SCREEN_WIDTH - 44 - 10, SCREEN_STATUSBAR_HEIGHT + 8, 28, 28) normalImage:@"image_chose" selectedImage:@"image_chose_up"];
        [_btn_topRight addTarget:self action:@selector(rightButtonAction:) forControlEvents:UIControlEventTouchUpInside];
        [self.topBarView addSubview:_btn_topRight];
        
    }
    
    [self.topBarView imy_addLanguageLayoutsChangeActionBlock:^(id weakObject) {
        @strongify(self);
        if (IMYISRTL) {
            self.btn_topRight.imy_left = 44 + 10 - 28;
        } else {
            self.btn_topRight.imy_right = SCREEN_WIDTH - 44 - 10 + 28;
        }
    }];
}

- (void)createMultipToolbar {
    _multipleToolbar = [[UIView alloc] initWithFrame:CGRectMake(0, self.view.imy_height - 50 - SCREEN_TABBAR_SAFEBOTTOM_MARGIN, SCREEN_WIDTH, 50 + SCREEN_TABBAR_SAFEBOTTOM_MARGIN)];
    _multipleToolbar.autoresizingMask = UIViewAutoresizingFlexibleTopMargin;
    _multipleToolbar.backgroundColor = [UIColor colorWithWhite:0 alpha:0.85];
    [self.view addSubview:_multipleToolbar];

    _btn_done = [IMYTouchEXButton buttonWithType:UIButtonTypeCustom];
    _btn_done.layer.cornerRadius = 17;
    _btn_done.layer.masksToBounds = YES;
    _btn_done.frame = CGRectMake(SCREEN_WIDTH - 78 - 16, 8, 78, 34);
    [_btn_done setContentEdgeInsets:UIEdgeInsetsMake(0, 12, 0, 12)];
    [_btn_done setExtendTouchInsets:UIEdgeInsetsMake(0, 5, 0, 5)];
    [_btn_done setBackgroundImage:[UIImage imy_imageFromColor:[UIColor imy_colorWithHexString:@"323232"] andSize:_btn_done.frame.size] forState:UIControlStateDisabled];
    [_btn_done setBackgroundImage:[UIImage imy_imageFromColor:[UIColor imy_colorForKey:kCK_Red_A] andSize:_btn_done.frame.size] forState:UIControlStateNormal];
    [_btn_done setTitle:self.chooseText forState:UIControlStateNormal];
    _btn_done.titleLabel.font = [UIFont systemFontOfSize:14];
    _btn_done.enabled = self.selectedAssetUrls.count > 0;
    [_multipleToolbar addSubview:_btn_done];
    [_btn_done addTarget:self action:@selector(done:) forControlEvents:UIControlEventTouchUpInside];
    
    [_btn_done mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(@8);
        make.height.mas_equalTo(34);
        make.trailing.equalTo(_multipleToolbar).mas_offset(-16);
        make.width.mas_greaterThanOrEqualTo(@78);
    }];

    if (self.selectedAssetUrls.count) {
        [self.btn_done setTitle:[NSString stringWithFormat:@"%@(%@)", self.chooseText, @(self.selectedAssetUrls.count)] forState:UIControlStateNormal];
    }
    
    UIButton *editBtn = [UIButton new];
    editBtn.enabled = NO;
    self.editBtn = editBtn;
    [editBtn setTitle:IMYString(@"编辑") forState:UIControlStateNormal];
    [editBtn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    [editBtn setTitleColor:[UIColor imy_colorWithHexString:@"999999"] forState:UIControlStateDisabled];
    editBtn.titleLabel.font = [UIFont systemFontOfSize:15];
    [_multipleToolbar addSubview:editBtn];
    [editBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(_btn_done);
        make.width.mas_equalTo(44);
        make.height.mas_equalTo(34);
        make.leading.equalTo(_multipleToolbar).mas_offset(12);
    }];
    editBtn.hidden = !self.showEdit;
    
    @weakify(self);
    [editBtn bk_addEventHandler:^(id sender) {
        @strongify(self);
        [self photoViewSingleTap:nil];
        imy_asyncMainBlock(0.25, ^{
            @strongify(self);
            [self editImageHandler];
        });
    } forControlEvents:UIControlEventTouchUpInside];
}

- (void)updateTopRightBtn {
    // 只有选取本地照片的预览才会走这里
    IMYPhoto *photo = self.photos[_currentPhotoIndex];
    if (photo.assetModel) {
        _btn_topRight.selected = [self.selectedAssetUrls containsObject:photo.assetModel];
    } else if (photo.albumUrl) {
        BOOL bSelected = NO;
        for (NSURL *url in self.selectedAssetUrls) {
            if ([url.absoluteString isEqualToString:photo.albumUrl.absoluteString]) {
                bSelected = YES;
                break;
            }
        }
        _btn_topRight.selected = bSelected;
    }
}

- (void)done:(id)sender {
    if (_selectCompleteBlock) {
        _selectCompleteBlock();
    }
}

- (void)editImageHandler {
    @weakify(self);
    
    [IMYGAEventHelper postWithPath:@"event" params:@{@"event":@"ttq_twfb_tpbj",@"action":@(2),@"topic_id":@(1)} headers:nil completed:nil];
    
    IMYPhoto *photo = self.photos[self.currentPhotoIndex];
    UIImage *cropOriImage;
    NSString *cropImagePath;
    
    NSDictionary *editInfo = [self.editedAssetMap objectForKey:photo.assetModel.identifier];
    id cropImage = [editInfo objectForKey:@"cropImageUrl"];
    if (cropImage) {
        if ([cropImage isKindOfClass:[NSString class]]) {
            cropImagePath = (NSString *)cropImage;
            cropOriImage = [UIImage imy_imageWithFilePath:cropImagePath];
        }else {
            cropOriImage = (UIImage *)cropImage;
        }
    }
    
    NSDictionary *param = @{@"image":cropOriImage ?:photo.image,
                            @"imageUrl":cropImagePath ? : photo.assetModel.identifier,
                            @"imageKeyPath":editInfo[@"imageKeyPath"] ?: @"",
                            @"modalStyle":@(UIModalPresentationOverFullScreen)};
    
    IMYURI *uri = [IMYURI uriWithPath:@"svr/image/edit" params:param info:nil];
    [[IMYURIManager shareURIManager] runActionWithURI:uri completed:^(IMYURIActionBlockObject *actionObject) {
        actionObject.implCallbackBlock = ^(NSDictionary *editImageInfo, NSError *error, NSString *eventName) {
            @strongify(self);
            [self photoViewSingleTap:nil];
            if ([eventName isEqualToString:@"event_cancle"]) {
                return;
            }
            
            NSString *cropImageUrl = editImageInfo[@"cropImageUrl"];
            NSString *imageKeyPath = editImageInfo[@"imageKeyPath"];
            NSNumber *isEdited = editImageInfo[@"isEdited"];//jer one
            UIImage *image = editImageInfo[@"image"];
            
            
            IMYPhoto *currentPhoto = self.photos[self.currentPhotoIndex];
            NSMutableDictionary *preEditInfo = [NSMutableDictionary dictionaryWithDictionary:[self.editedAssetMap objectForKey:currentPhoto.assetModel.identifier]?:@{}];
            //编辑图片有触发裁剪操作
            if (cropImageUrl.length) {
                id preCropImage = [preEditInfo objectForKey:@"cropImageUrl"];
                //所编辑的图片不是第一次进行裁剪，把之前沙盒中裁剪的图片删掉
                if (preCropImage && [preCropImage isKindOfClass:[NSString class]]) {
                    NSFileManager *fm = [NSFileManager defaultManager];
                    [fm removeItemAtPath:(NSString *)preCropImage error:NULL];
                }
                [preEditInfo setObject:cropImageUrl forKey:@"cropImageUrl"];
            }else {//编辑图片没有触发裁剪操作
                if (![preEditInfo objectForKey:@"cropImageUrl"]) {
                    [preEditInfo setObject:currentPhoto.image forKey:@"cropImageUrl"];
                }
            }
    
            currentPhoto.image = image;
            
            [preEditInfo setObject:image forKey:@"image"];
            [preEditInfo setObject:imageKeyPath forKey:@"imageKeyPath"];
            [preEditInfo setObject:isEdited forKey:@"isEdited"];//jer one
            [self.editedAssetMap setObject:preEditInfo forKey:currentPhoto.assetModel.identifier];
            
            if (self.didEditedImageHandler) {
                self.didEditedImageHandler(currentPhoto.assetModel.identifier);
            }
            
            [self reloadData];
            
            //编辑后的图片默认选中
            if (![self.selectedAssetUrls containsObject:currentPhoto.assetModel]) {
                [self rightButtonAction:self.btn_topRight];
            }
        };
    }];
}

#pragma mark - 竖直返回手势处理

- (BOOL)gestureRecognizerShouldBegin:(UIPanGestureRecognizer *)gestureRecognizer {
    if (![gestureRecognizer isKindOfClass:UIPanGestureRecognizer.class]) {
        return YES;
    }
    CGPoint velocity = [gestureRecognizer velocityInView:self.view];
    if (fabs(velocity.y) < fabs(velocity.x)) {
        /// 返回手势只考虑纵向手势
        return NO;
    }
    IMYPhotoView *photoView = [self currentPhotoView];
    if (photoView.contentSize.height > photoView.imy_height) {
        /// 长图
        if (velocity.y > 0 && photoView.contentOffset.y > 0) {
            /// 向下拉时
            return NO;
        }
        if (velocity.y < 0 && photoView.contentOffset.y < (photoView.contentSize.height - photoView.imy_height)) {
            /// 向上拉
            return NO;
        }
    }
    
    if (self.allowSelectionVideo && photoView.photo.assetModel.assetType == IMYAssetTypeVideo) {
        // 如果是视频，操作区不允许拖动
        UIView *actionBar = [photoView imy_findSubviewWithClass:IMYPhotoVideoActionBar.class];
        if (actionBar && !actionBar.hidden) {
            CGPoint point = [gestureRecognizer locationInView:actionBar];
            if (CGRectContainsPoint(actionBar.bounds, point)) {
                return NO;
            }
        }
    }
    return YES;
}

- (void)pan:(UIPanGestureRecognizer *)gestureRecognizer {
    CGPoint translation = [gestureRecognizer translationInView:gestureRecognizer.view.superview];
    CGPoint velocity = [gestureRecognizer velocityInView:self.view];
    switch (gestureRecognizer.state) {
        case UIGestureRecognizerStateBegan:
            self.resetPanGestureDirection = YES;
            self.shouldCompletePopAnimation = NO;
            if (fabs(translation.x) < fabs(translation.y) && self.showType != IMYBrowserTypeFeedsOversea) {
                self.toolbar.indexLabel.hidden = YES;
            }
            [[UIApplication sharedApplication] setStatusBarHidden:_statusBarHiddenInited withAnimation:UIStatusBarAnimationNone];
            if (self.animatedTransition) {
                /// 返回上级页面手势
                ((IMYPublicBaseNavigationController *)self.navigationController).wrapDelegate = self.animatedTransition;
                self.animatedTransition.gestureRecognizer = gestureRecognizer;
                IMYPhoto *curPhoto = self.photos[self.currentPhotoIndex];
                /// 设置手势结束，图片返回的位置
                self.animatedTransition.beforeImageViewFrame = curPhoto.assetListFrameInScreen;
                [self.navigationController popViewControllerAnimated:YES];
            }
            break;
        case UIGestureRecognizerStateChanged: {
            self.photoScrollView.panGestureRecognizer.enabled = NO;
            if (self.resetPanGestureDirection) {
                self.popVertical = fabs(translation.x) < fabs(translation.y);
                self.resetPanGestureDirection = NO;
            }
            if (self.popVertical || self.currentPhotoIndex > 0) {
                [self panGestureMove:translation];
            }
            break;
        }
        case UIGestureRecognizerStateEnded:
        case UIGestureRecognizerStateCancelled: {
            if (velocity.y > 500) {
                // 判断是不是快速下拉，做swipe手势的支持
                [self verticalPanGestureEnd];
            } else if (self.shouldCompletePopAnimation) {
                [self verticalPanGestureEnd];
            } else {
                [self cancelPanGesture];
            }
            self.photoScrollView.panGestureRecognizer.enabled = YES;
            self.popVertical = NO;
            if (self.animatedTransition) {
                /// 手势结束
                ((IMYPublicBaseNavigationController *)self.navigationController).wrapDelegate = nil;
            }
            break;
        }
        default:
            self.popVertical = NO;
            self.photoScrollView.panGestureRecognizer.enabled = YES;
            break;
    }
}

- (void)panGestureMove:(CGPoint)offset {
    if (fabs(offset.y) < kVerticalGestureLimitOffSet) {
        return;
    }
    if (self.animatedTransition && offset.y < 0) {
        /// 返回手势，不能往上移，参考微信
        offset.y = 0;
    }
    offset.x = MIN(SCREEN_WIDTH / 2.0, offset.x);
    offset.x = MAX(-SCREEN_WIDTH / 2.0, offset.x);
    offset.y = MIN(SCREEN_HEIGHT / 2.0, offset.y);
    offset.y = MAX(-SCREEN_HEIGHT / 2.0, offset.y);
    
    CGPoint imageCenter = CGPointMake(SCREEN_WIDTH / 2.0 + offset.x, SCREEN_HEIGHT / 2.0 + offset.y);
    CGFloat percent = fabs(offset.y / (self.view.imy_height / 2));
    percent = fminf(fmaxf(percent, 0.0), 1.0);
    
    self.photoScrollView.transform = CGAffineTransformMakeScale(1- percent * 0.5, 1- percent * 0.5);
    self.photoScrollView.center = imageCenter;
    if (self.showType == IMYBrowserTypeCustomView) {
        self.toolbar.imy_top = kCustomView_toolbar_y - fabs(offset.y);
        self.footerView.imy_bottom = self.view.imy_height + fabs(offset.y);
    } else if(self.showType == IMYBrowserTypeFeedsOversea){
        self.toolbar.imy_top = SCREEN_STATUSBAR_HEIGHT - fabs(offset.y);
    }
    else {
        self.toolbar.imy_bottom = self.view.imy_height - SCREEN_TABBAR_SAFEBOTTOM_MARGIN + fabs(offset.y);
    }
    self.coverView.alpha = 1 - percent * 0.5;// 修改遮罩透明度
    self.shouldCompletePopAnimation = percent > 0.1 ? YES : NO;// 如果偏移量非常大，手势结束直接让图片消失
}

// 拖动手势结束后
    // 如果当前预览图片在帖子的可视范围内，需要有回到帖子原图的效果
    // 如果当前预览图片不在帖子的可视范围内，直接在松手的位置渐隐
    // 目前缺陷，除了触发【大图预览】的那张图能回到初始位置，其他图片都不行
- (void)verticalPanGestureEnd {
    if ([self.delegate respondsToSelector:@selector(photoBrowserWillHide:)]) {
        [self.delegate photoBrowserWillHide:self];
    }
    [self photoViewSingleTap:nil];
    IMYPhoto *curPhoto = self.photos[self.currentPhotoIndex];
    if (self.animatedTransition) {
        IMYPhotoView *curPhotoView = [self currentPhotoView];
        self.animatedTransition.currentImageView = curPhotoView.imageView;
        self.animatedTransition.currentImageViewFrame = [curPhotoView.imageView convertRect:curPhotoView.imageView.bounds toView:nil];
        if (!self.animatedTransition.currentContextTransitioning) {
            /// 有个异常场景，斜角快速下拉时，转动动画没有获取到对应的context，导致达到了消失条件却无法消失的bug，需要做下取消
            [self cancelPanGesture];
        }
    } else {
        [UIView animateWithDuration:0.3
            animations:^{
                IMYPhotoView *curPhotoView = [self currentPhotoView];
                if (!curPhotoView) {
                    return ;
                }
                self.coverView.alpha = 0;// 修改遮罩透明度
                UIView *frameView = curPhoto.srcSuperView ?: curPhoto.srcImageView;
                CGRect targetFrame = [frameView convertRect:frameView.bounds toView:nil];
                // 判断目标位置可视，这里判断比较弱，无法判断是不是被其它视图遮挡
                BOOL visible = CGRectIntersectsRect(CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT), targetFrame);
                if (frameView != nil && frameView.window != nil && visible) {
                    // 将预览图image部分的坐标往源image的位置过度
                    // 前面移动的是scrollview的位置
                    self.photoScrollView.transform = CGAffineTransformIdentity;
                    self.photoScrollView.imy_centerX = SCREEN_WIDTH / 2.0;
                    self.photoScrollView.imy_centerY = SCREEN_HEIGHT / 2.0;
                    if (curPhotoView.zoomScale != 1) {
                        [curPhotoView setZoomScale:1 animated:YES];
                    }
                    curPhotoView.imageView.contentMode = frameView.contentMode;
                    curPhotoView.imageView.frame = targetFrame;
                } else {
                    curPhotoView.imageView.alpha = 0;
                }
                // gif图片仅显示第0张
                if (curPhotoView.imageView.image.images) {
                    curPhotoView.imageView.image = curPhotoView.imageView.image.images[0];
                }
            }
            completion:^(BOOL finished) {
    //            curPhoto.srcImageView.hidden = NO;
                [self photoViewDidEndZoom:nil];
        }];
    }
}

- (void)cancelPanGesture {
    [UIView animateWithDuration:0.3
         delay:0
         options:UIViewAnimationOptionCurveEaseOut
         animations:^{
             self.coverView.alpha = 1;
             self.photoScrollView.imy_centerX = SCREEN_WIDTH / 2.0;
             self.photoScrollView.imy_centerY = SCREEN_HEIGHT / 2.0;
             self.photoScrollView.transform = CGAffineTransformIdentity;
             if (self.showType == IMYBrowserTypeCustomView) {
                 self.toolbar.imy_top = kCustomView_toolbar_y;
                 self.footerView.imy_bottom = self.view.imy_height;
             } else if(self.showType == IMYBrowserTypeFeedsOversea){
                 self.toolbar.imy_top = SCREEN_STATUSBAR_HEIGHT;
             }
             else {
                 self.toolbar.imy_bottom = self.view.imy_height - SCREEN_TABBAR_SAFEBOTTOM_MARGIN;
             }
         }
         completion:^(BOOL finished) {
            self.toolbar.indexLabel.hidden = NO;
            ((IMYPublicBaseNavigationController *)self.navigationController).wrapDelegate = self;
            [[UIApplication sharedApplication] setStatusBarHidden:YES withAnimation:UIStatusBarAnimationNone];
        
         }];
    if (self.animatedTransition) {

    }
}

#pragma mark - iCloud indicatorView 相关逻辑

- (void)showiCloudIndicatorWithAssetModel:(IMYAssetModel *)assetModel {
    if (!self.needShowiCloudIndicatorView) {
        return;
    }
    
    @weakify(self);
    imy_asyncMainBlock(^{
        @strongify(self);
        if (!self.iCloudIndicatorView) {
            self.iCloudIndicatorView = [[IMYPhotoiCloudIndicatorView alloc] init];
            [self.iCloudIndicatorView setRefreshBlock:^(IMYAssetModel * _Nonnull assetModel) {
                @strongify(self);
                if (self.imy_topRightButton) {
                    if (assetModel.iCloudAssetModel.status != IMYiCloudAssetModel_Status_inLocal) {
                        self.btn_topRight.hidden = YES;
                    } else {
                        self.btn_topRight.hidden = NO;
                    }
                }
            }];
            [self.view addSubview:self.iCloudIndicatorView];
            [self.iCloudIndicatorView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.top.mas_equalTo(self.view.mas_top).offset(SCREEN_STATUSBAR_HEIGHT);
                make.trailing.mas_equalTo(self.view.mas_trailing).offset(-8);
                make.height.mas_equalTo(44);
            }];
        }
        
        [self.view bringSubviewToFront:self.iCloudIndicatorView];
        [self.iCloudIndicatorView refreshWithAssetModel:assetModel];
        
        if (self.imy_topRightButton) {
            if (assetModel.iCloudAssetModel.status != IMYiCloudAssetModel_Status_inLocal) {
                self.btn_topRight.hidden = YES;
            } else {
                self.btn_topRight.hidden = NO;
            }
        }
    });
}

@end

@implementation IMYPhotoMainScrollView

- (BOOL)gestureRecognizerShouldBegin:(UIGestureRecognizer *)gestureRecognizer {
    BOOL shouldBegin = [super gestureRecognizerShouldBegin:gestureRecognizer];
    if (shouldBegin && [gestureRecognizer isKindOfClass:UIPanGestureRecognizer.class] && self.scrollEnabled && [(IMYPhotoBrowser *)self.delegate allowSelectionVideo]) {
        IMYPhotoView *photoView = [(IMYPhotoBrowser *)self.delegate currentPhotoView];
        if (photoView.photo.assetModel.assetType == IMYAssetTypeVideo) {
            // 如果是视频，操作区不允许拖动
            UIView *actionBar = [photoView imy_findSubviewWithClass:IMYPhotoVideoActionBar.class];
            if (actionBar && !actionBar.hidden) {
                CGPoint point = [gestureRecognizer locationInView:actionBar];
                if (CGRectContainsPoint(actionBar.bounds, point)) {
                    shouldBegin = NO;
                }
            }
        }
    }
    return shouldBegin;
}

- (UIView *)hitTest:(CGPoint)point withEvent:(UIEvent *)event {
    /*
     直接拖动UISlider，此时touch时间在150ms以内，UIScrollView会认为是拖动自己，从而拦截了event，导致UISlider接受不到滑动的event。但是只要按住UISlider一会再拖动，此时此时touch时间超过150ms，因此滑动的event会发送到UISlider上。
    */
    UIView *hitView = [super hitTest:point withEvent:event];
    if (self.scrollEnabled && [(IMYPhotoBrowser *)self.delegate allowSelectionVideo]) {
        UIView *actionBar = [hitView imy_findParentViewWithClass:IMYPhotoVideoActionBar.class];
        if (actionBar && !actionBar.hidden) {
            //如果 响应view 属于视频播放控制栏，则 scrollview 禁止滑动
            self.scrollEnabled = NO;
            imy_asyncMainBlock(0.1, ^{
                self.scrollEnabled = YES;
            });
        }
    }
    
    return hitView;
}

@end
