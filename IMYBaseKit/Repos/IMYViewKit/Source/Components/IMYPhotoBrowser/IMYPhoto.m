//
//  IMYPhoto.m
//  IMYViewKit
//
//  Created by <PERSON><PERSON> on 15/6/3.
//  Copyright (c) 2015年 IMY. All rights reserved.
//

#import "IMYPhoto.h"
#import "IMYViewKit.h"

@implementation IMYPhoto
@synthesize placeholder = _placeholder;

+ (UIImage *)IMYPlaceholder {
    return [UIImage imy_imageFromColor:IMY_COLOR_KEY(kCK_Black_F)
                               andSize:CGSizeMake(SCREEN_WIDTH, SCREEN_WIDTH)];
}

+ (UIImage *)IMYPlaceholderOversea {
    return [UIImage imy_imageFromColor:IMY_COLOR_KEY(kCK_Clear_A)
                               andSize:CGSizeMake(SCREEN_WIDTH, SCREEN_WIDTH)];
}

- (id)init {
    self = [super init];
    if (self) {
        _placeholder = [self.class IMYPlaceholder];
    }
    return self;
}
- (void)setUrl:(NSURL *)url {
    _url = [NSURL URLWithString:[NSString qiniuURL:url type:IMY_QiNiu_WEBP]];
    // 查看大图，每次头像都加上时间戳避免CDN缓存
    if ([IMYMeetyouHTTPHooks isMeiYouSite:_url.host] && [_url.path hasPrefix:@"/avatar"]) {
        self.isAvatar = YES;
        BOOL containQuestion = [_url.absoluteString containsString:@"?"];
        NSString *urlString = [_url.absoluteString stringByAppendingFormat:@"%@timestamp=%.0lf", (containQuestion ? @"&" : @"?"), IMYDateTimeIntervalSince1970()];
        _url = [NSURL URLWithString:urlString];
    }
}

- (NSURL *)albumUrl {
    return nil;
//    if (!_albumUrl) {
//        NSURL *url = _asset.defaultRepresentation.url;
//        
//        _albumUrl = url;
//    }
//    return _albumUrl;
}

#pragma mark 截图
- (UIImage *)capture:(UIView *)view {
    UIGraphicsBeginImageContextWithOptions(view.bounds.size, YES, 0.0);
    [view.layer renderInContext:UIGraphicsGetCurrentContext()];
    UIImage *img = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    return img;
}

- (void)setSrcImageView:(UIImageView *)srcImageView {
    _srcImageView = srcImageView;
    _placeholder = srcImageView.image;
    if (srcImageView.clipsToBounds) {
        //        _capture = [self capture:srcImageView];
    }
}

- (UIImage *)placeholder {
    if (_isOverseaFailImage) {
        return [self.class IMYPlaceholderOversea];
    }
    return (_isAvatar ? self.srcImageView.image : _sy_placeholder) ?: [self.class IMYPlaceholder];
}

- (void)dealloc {
}
@end
