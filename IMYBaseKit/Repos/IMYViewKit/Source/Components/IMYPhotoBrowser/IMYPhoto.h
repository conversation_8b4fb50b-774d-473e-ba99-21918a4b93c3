//
//  IMYPhoto.h
//  IMYViewKit
//
//  Created by Frank<PERSON>lin on 15/6/3.
//  Copyright (c) 2015年 IMY. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

@class IMYAssetModel;

@interface IMYPhoto : NSObject

// 先使用 fileurl 获取图片  获取失败   使用 url 进行下载
@property (nonatomic, strong) NSURL *fileurl;
// 远程资源
@property (nonatomic, strong) NSURL *url;
// 本地相册
@property (nonatomic, strong) IMYAssetModel *assetModel;
@property (nonatomic, assign) BOOL isBrowser; // 属于大图浏览模式

// 完整的图片
@property (nonatomic, strong) UIImage *image;

// 点击图片直接进入大图预览时，此属性用于保存来源图片控件的大小，位置等信息
@property (nonatomic, weak) UIImageView *srcImageView; // 来源view

@property (nonatomic, strong) UIView *srcSuperView; // 来源view

@property (nonatomic, strong, readonly) UIImage *placeholder;
@property (nonatomic, strong, readonly) UIImage *capture;

@property (nonatomic, assign) BOOL firstShow;

// 是否已经保存到相册
@property (nonatomic, assign) BOOL save;
@property (nonatomic, assign) int index; // 索引
@property (nonatomic, assign) BOOL isAvatar;
@property (nonatomic, assign) BOOL isOverseaFailImage;
@property (nonatomic, strong) UIImage *sy_placeholder;

@property (nonatomic, strong) NSURL *albumUrl;
@property (nonatomic, assign) BOOL disableAnimation;

/// 在屏幕坐标下相册列表里的frame;用于手势下拉返回pop动画
@property (nonatomic, assign) CGRect assetListFrameInScreen;
@property (nonatomic, assign) BOOL isLongImg;

@end
