//
//  IMYPhotoView.m
//  IMYViewKit
//
//  Created by <PERSON><PERSON>l<PERSON> on 15/6/3.
//  Copyright (c) 2015年 IMY. All rights reserved.
//

#import "IMYPhotoView.h"
#import "IMYAssetModel.h"
#import "IMYAvatarImageView.h"
#import "IMYPhoto.h"
#import "IMYPhotoLoadingView.h"
#import "IMYViewKit.h"
#import "UIView+WebCacheOperation.h"
#import "IMYPhotoVideoActionBar.h"

/// 匹配url上的图片尺寸 以 xx_121_123.xxx为识别标识
static NSString *kImageSizeUrlRegex = @"_([0-9]{1,})_([0-9]{1,})\\.";

@implementation IMYPhotoViewScrollView

#pragma mark - UIGestureRecognizerDelegate
- (BOOL)gestureRecognizerShouldBegin:(UIGestureRecognizer *)gestureRecognizer {
    if (gestureRecognizer == self.panGestureRecognizer) {
        if (gestureRecognizer.state == UIGestureRecognizerStatePossible) {
            if ([self isScrollViewOnTopOrBottom]) {
                return NO;
            }
        }
        
        if (self.scrollEnabled && self.allowSelectionVideo) {
            UIView *actionBar = [self imy_findSubviewWithClass:IMYPhotoVideoActionBar.class];
            if (actionBar && !actionBar.hidden) {
                CGPoint point = [gestureRecognizer locationInView:actionBar];
                if (CGRectContainsPoint(actionBar.bounds, point)) {
                    return NO;
                }
            }
        }
    }
    
    return YES;
}

// 判断是否滑动到顶部或底部
- (BOOL)isScrollViewOnTopOrBottom {
    CGPoint translation = [self.panGestureRecognizer translationInView:self];
    if (translation.y > 0 && self.contentOffset.y <= 0) {
        return YES;
    }
    CGFloat maxOffsetY = floor(self.contentSize.height - self.bounds.size.height);
    if (translation.y < 0 && self.contentOffset.y >= maxOffsetY) {
        return YES;
    }
    return NO;
}

- (UIView *)hitTest:(CGPoint)point withEvent:(UIEvent *)event {
    UIView *hitView = [super hitTest:point withEvent:event];
    if (self.scrollEnabled && self.allowSelectionVideo) {
        /// 修复拖动 UISlider 卡顿的问题
        UIView *actionBar = [hitView imy_findParentViewWithClass:IMYPhotoVideoActionBar.class];
        if (actionBar && !actionBar.hidden) {
            //如果 响应view 属于视频播放控制栏，则 scrollview 禁止滑动
            self.scrollEnabled = NO;
            imy_asyncMainBlock(0.1, ^{
                self.scrollEnabled = YES;
            });
        }
    }
    return hitView;
}

@end

#import "IMYPhotoVideoPlayerView.h"

@interface IMYPhotoView () {
    BOOL _doubleTap;
    IMYPhotoLoadingView *_photoLoadingView;
    BOOL disableLayoutSubvies;
    BOOL _zoom;
    CGRect _initialFrame;
}

@property (nonatomic, assign) BOOL presentingAnimation;
@property (nonatomic, strong) IMYPhotoVideoPlayerView *videoPlayer;
@property (nonatomic, strong) UIButton *videoCloseBtn;

@property (nonatomic, assign) NSInteger maxRetryCount; // 默认最多重试5次
@property (nonatomic, assign) NSInteger currentRetryCount; // 当前重试次数

@end

@implementation IMYPhotoView

- (instancetype)initWithFrame:(CGRect)frame {
    if ((self = [super initWithFrame:frame])) {
        self.clipsToBounds = YES;
        
        self.maxRetryCount = 5;
        self.currentRetryCount = 0;
        
        self.presentingAnimation = NO;
        // 图片
        {
            IMYAvatarImageView *avatarImageView = [[IMYAvatarImageView alloc] init];
            avatarImageView.showBigImage = YES;

            ///孕期头像采用圆形处理 by 店长
            avatarImageView.needShowCicrle = self.needShowCicrle;

            _imageView = avatarImageView;
        }
        _imageView.contentMode = UIViewContentModeScaleAspectFit;
        [self addSubview:_imageView];
        
        // 视频
        {
            self.videoPlayer = [IMYPhotoVideoPlayerView new];
            self.videoPlayer.hidden = YES;
            [self addSubview:self.videoPlayer];
            
            // 左上角加个关闭按钮all_top_close
            self.videoCloseBtn = [UIButton buttonWithType:UIButtonTypeCustom];
            self.videoCloseBtn.hidden = YES;
            self.videoCloseBtn.frame = CGRectMake(16, 10 + SCREEN_STATUSBAR_HEIGHT, 24, 24);
            [self.videoCloseBtn setImage:[UIImage imy_imageForKey:@"all_top_close"] forState:UIControlStateNormal];
            self.videoCloseBtn.hidden = YES;
            [self.videoCloseBtn addTarget:self action:@selector(closeVideoView:) forControlEvents:UIControlEventTouchUpInside];
            [self addSubview:self.videoCloseBtn];
        }
        
        // 进度条
        _photoLoadingView = [[IMYPhotoLoadingView alloc] init];

        // 属性
        self.backgroundColor = [UIColor clearColor];
        self.delegate = self;
        self.showsHorizontalScrollIndicator = NO;
        self.showsVerticalScrollIndicator = NO;
        self.decelerationRate = UIScrollViewDecelerationRateFast;
        self.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;

        // 监听点击
        UITapGestureRecognizer *singleTap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(handleSingleTap:)];
        singleTap.delaysTouchesBegan = YES;
        singleTap.numberOfTapsRequired = 1;
        [self addGestureRecognizer:singleTap];

        UITapGestureRecognizer *doubleTap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(handleDoubleTap:)];
        doubleTap.numberOfTapsRequired = 2;
        [self addGestureRecognizer:doubleTap];

        UILongPressGestureRecognizer *longPressGestureRecognizer = [[UILongPressGestureRecognizer alloc] initWithTarget:self action:@selector(longPress:)];
        [self addGestureRecognizer:longPressGestureRecognizer];
    }
    return self;
}

#pragma mark - photoSetter

- (void)didMoveToSuperview {
    [super didMoveToSuperview];
    if (!self.superview) {
        // 不在显示范围，停止播放
        [self.videoPlayer cancelPlay];
    }
}

- (void)setPhoto:(IMYPhoto *)photo {
    _photo = photo;
    _zoom = NO;
    if (_overseaFailLoad) {
        _photo.isOverseaFailImage = YES;
    }
    if (self.allowSelectionVideo && _photo.assetModel.assetType == IMYAssetTypeVideo) {
        _videoPlayer.autoPlayVideo = self.autoPlayVideo;
        _videoPlayer.assetModel = _photo.assetModel;
        _videoPlayer.hidden = NO;
    } else {
        _videoPlayer.assetModel = nil;
        _videoPlayer.hidden = YES;
    }
    [self showImage];
    [self fixFrame];
}

#pragma mark 显示图片

- (void)loadImageForAssetModel:(UIActivityIndicatorView *)juhua {
    @weakify(self);
    
    // 先读取缩略图
    CGRect bounds = _imageView.bounds;
    bounds.size = CGSizeMake(SCREEN_WIDTH, SCREEN_HEIGHT);
    _imageView.frame = bounds;
    _imageView.image = nil;
    [_photo.assetModel requestThumbnailImageWithSize:bounds.size completion:^(UIImage * _Nonnull result, NSDictionary<NSString *,id> * _Nonnull info) {
        @strongify(self);
        imy_asyncMainExecuteBlock(^{
            if (!self.photo.image) {
                self.imageView.image = result;
            }
        });
    }];
    
    if (_photo.assetModel.assetSubType  == IMYAssetSubTypeGIF) {
        [_photo.assetModel requestImageData:^(NSData * _Nonnull imageData, NSDictionary<NSString *,id> * _Nonnull info, BOOL isGIF, BOOL isHEIC) {
            YYImage *image = [YYImage imageWithData:imageData];
            imy_asyncMainExecuteBlock(^{
                @strongify(self);
                if (image != nil) {
                    self.photo.image = image;
                    self.imageView.image = self.photo.image;
                    
                    [self adjustFrame];
                    [juhua stopAnimating];
                    [juhua removeFromSuperview];
                    if ([self.photoViewDelegate respondsToSelector:@selector(photoViewImageFinishLoad:)]) {
                        [self.photoViewDelegate photoViewImageFinishLoad:self];
                    }
                } else {
                    [self adjustFrame];
                    [juhua stopAnimating];
                    [juhua removeFromSuperview];
                }
            });
        }];
    } else {
        [_photo.assetModel requestFullScreenImageWithCompletion:^(UIImage *_Nonnull result, NSDictionary<NSString *, id> *_Nonnull info) {
            imy_asyncMainExecuteBlock(^{
                @strongify(self);
                BOOL loadICloudImageFault = !result || info[PHImageErrorKey] != nil;
                if (!loadICloudImageFault) {
                    // 有的图片可能由于icloud的问题下载不到原图，result返回空
                    self.photo.image = result;
                    self.imageView.image = self.photo.image;
                }
                
                BOOL downloadSucceed = (result.size.width > 0 && result.size.height > 0) || (![[info objectForKey:PHImageCancelledKey] boolValue] && ![info objectForKey:PHImageErrorKey] && ![[info objectForKey:PHImageResultIsDegradedKey] boolValue]);
                if (downloadSucceed) {
                    [self adjustFrame];
                    [juhua stopAnimating];
                    [juhua removeFromSuperview];
                    if ([self.photoViewDelegate respondsToSelector:@selector(photoViewImageFinishLoad:)]) {
                        [self.photoViewDelegate photoViewImageFinishLoad:self];
                    }
                } else {
                    [self adjustFrame];
                    [juhua stopAnimating];
                    [juhua removeFromSuperview];
                }
            });
        } andProgressHandler:nil];
    }
}

- (void)loadImageForALAssetURL:(UIActivityIndicatorView *)juhua {
    imy_asyncBlock(^{
//        @try {
//            ALAssetsLibrary *assetslibrary = [[ALAssetsLibrary alloc] init];
//            [assetslibrary assetForURL:_photo.albumUrl
//                           resultBlock:^(ALAsset *asset) {
//                ALAssetRepresentation *rep = [asset defaultRepresentation];
//                int imgHeight = ((NSNumber *)rep.metadata[@"PixelHeight"]).intValue;
//                int imgWitdh = ((NSNumber *)rep.metadata[@"PixelWidth"]).intValue;
//                if (imgHeight==0 && imgWitdh==0) {
//                    CGSize size = rep.dimensions;
//                    imgHeight = size.height;
//                    imgWitdh = size.width;
//                }
//                CGImageRef iref;
//                if (imgWitdh <= 0) {
//                    iref = nil;
//                } else {
//                    iref = imgHeight / imgWitdh > 3 ? [rep fullResolutionImage] : [rep fullScreenImage];
//                }
//                
//                if (iref) {
//                    UIImage *image = nil;
//                    if (imgHeight / imgWitdh > 3) {
//                        image = [UIImage imageWithCGImage:iref scale:rep.scale orientation:(UIImageOrientation)rep.orientation];
//                    } else {
//                        image = [UIImage imageWithCGImage:iref];
//                    }
//                    imy_asyncMainBlock(^{
//                        self.photo.image = image;
//                        self.imageView.image = self.photo.image;
//                        [self adjustFrame];
//                        [juhua stopAnimating];
//                        [juhua removeFromSuperview];
//                    });
//                } else {
//                    self.photo.image = [UIImage imageWithCGImage:asset.aspectRatioThumbnail];
//                    imy_asyncMainBlock(^{
//                        self.imageView.image = self.photo.image;
//                        [self adjustFrame];
//                        [juhua stopAnimating];
//                        [juhua removeFromSuperview];
//                    });
//                }
//            } failureBlock:^(NSError *error) {
//                imy_asyncMainBlock(^{
//                    self.photo.image = nil;
//                    self.imageView.image = nil;
//                    [self adjustFrame];
//                    [juhua stopAnimating];
//                    [juhua removeFromSuperview];
//                });
//            }];
//        } @catch (NSException *e) {
//            imy_asyncMainBlock(^{
//                self.photo.image = nil;
//                self.imageView.image = nil;
//                [self adjustFrame];
//                [juhua stopAnimating];
//                [juhua removeFromSuperview];
//            });
//        }
    });
}

- (void)loadImageForFirstShow {
    // 不是gif，就马上开始下载
    if (![_photo.url.absoluteString hasSuffix:@"gif"]) {
        __weak IMYPhotoView *photoView = self;
        __weak IMYPhoto *photo = _photo;
        __weak UIImageView *wview = _imageView;
        
        if (photo.isAvatar) {
            [wview sd_setImageWithURL:photo.url
                     placeholderImage:photo.placeholder
                              options:SDWebImageRetryFailed | SDWebImageLowPriority
                            completed:^(UIImage *image, NSError *error, SDImageCacheType cacheType, NSURL *imageURL) {
                photo.image = image;
                [photoView adjustFrame];
            }];
        } else {
            if (_photo.fileurl) {
                [wview sd_setImageWithURL:_photo.fileurl
                         placeholderImage:_photo.placeholder
                                completed:^(UIImage *image, NSError *error, SDImageCacheType cacheType, NSURL *imageURL) {
                    if (image == nil) {
                        [wview sd_setImageWithURL:photo.url
                                 placeholderImage:photo.placeholder
                                          options:SDWebImageRetryFailed | SDWebImageLowPriority
                                        completed:^(UIImage *image, NSError *error, SDImageCacheType cacheType, NSURL *imageURL) {
                            photo.image = image;
                            [photoView adjustFrame];
                        }];
                    } else {
                        photo.image = image;
                        [photoView adjustFrame];
                    }
                }];
            } else {
                UIImage *placeholder = photo.placeholder;
                CGSize urlSize = [self getImageSizeFromUrl];
                if (!photo.sy_placeholder && !CGSizeEqualToSize(CGSizeZero, urlSize)) {
                    // 没有传默认图，需要下载时就以目标大小做放大动画
                    // 这里sd那边设置placeholder时，会image的尺寸展示，而不是imageView的尺寸，看源码，在设置了clipToBounds的情况下不应该这样，先按修改默认图尺寸在处理了。
                    /// url读取的地址可能过大，如5000*3000，需要适配下
                    if (urlSize.width > self.imy_width) {
                        urlSize = CGSizeMake(self.imy_width, ceilf(self.imy_width*urlSize.height/urlSize.width));
                    }
                    if (urlSize.height > self.imy_height) {
                        urlSize = CGSizeMake(ceilf(self.imy_height*urlSize.width/urlSize.height), self.imy_height);
                    }
                    placeholder = [placeholder imy_scaledImageWithSize:urlSize];
                    photo.sy_placeholder = placeholder;
                }
                [wview sd_setImageWithURL:photo.url
                         placeholderImage:placeholder
                                  options:SDWebImageRetryFailed | SDWebImageLowPriority
                                completed:^(UIImage *image, NSError *error, SDImageCacheType cacheType, NSURL *imageURL) {
                    photo.image = image;
                    [photoView adjustFrame];
                }];
            }
        }
    }
}

- (void)showImage {
    if ((_photo.assetModel || _photo.albumUrl) || _photo.disableAnimation) {
        UIView *lastjuhua = [self viewWithTag:101];
        if (lastjuhua) {
            [lastjuhua removeFromSuperview];
        }
        if (_photo.image) {
            _imageView.image = _photo.image;
            [self adjustFrame];
        } else {
            _imageView.image = nil;
            UIActivityIndicatorView *juhua = [[UIActivityIndicatorView alloc] initWithActivityIndicatorStyle:UIActivityIndicatorViewStyleWhiteLarge];
            juhua.center = CGPointMake(SCREEN_WIDTH / 2, SCREEN_HEIGHT / 2);
            juhua.tag = 101;
            [juhua startAnimating];
            [self addSubview:juhua];

            if (_photo.assetModel) {
                [self loadImageForAssetModel:juhua];
            } else {
                [self loadImageForALAssetURL:juhua];
            }
        }
        return;
    }
    if (_photo.firstShow) { // 首次显示
        if (_photo.image) {
            _imageView.image = _photo.image;
            [self adjustFrame];
        } else {
            if (_photo.srcImageView.image) {
                _imageView.image = _photo.srcImageView.image;
                _photo.sy_placeholder = _imageView.image;
            } else {
                _imageView.image = _photo.placeholder; // 占位图片
            }
            [self loadImageForFirstShow];
        }
    } else {
        [self photoStartLoad];
    }
    // 调整frame参数
    [self adjustFrame];
}

#pragma mark 开始加载图片

- (void)photoStartLoad {
    if (_photo.image) {
        self.scrollEnabled = YES;
        _imageView.image = _photo.image;
        [self photoDidFinishLoadWithImage:_photo.image error:nil];
    } else {
        self.scrollEnabled = NO;
        // 直接显示进度条
        if (self.overseaFailLoad) {
            [_photoLoadingView showLightStrokeLoading];
        } else
            [_photoLoadingView showLoading];
        [self addSubview:_photoLoadingView];


        __weak IMYPhotoView *photoView = self;
        __weak IMYPhotoLoadingView *loading = _photoLoadingView;
        __weak IMYPhoto *photo = _photo;
        __weak UIImageView *wview = _imageView;
        if (_photo.fileurl) {
            [wview sd_setImageWithURL:_photo.fileurl
                     placeholderImage:_photo.placeholder
                            completed:^(UIImage *image, NSError *error, SDImageCacheType cacheType, NSURL *imageURL) {
                                if (image == nil) {
                                    [wview sd_setImageWithURL:photo.url
                                        placeholderImage:photo.placeholder
                                        options:SDWebImageRetryFailed | SDWebImageLowPriority
                                        progress:^(NSInteger receivedSize, NSInteger expectedSize) {
                                            if (receivedSize > kMinProgress) {
                                                loading.progress = (float)receivedSize / expectedSize;
                                            }
                                        }
                                        completed:^(UIImage *image, NSError *error, SDImageCacheType cacheType, NSURL *imageURL) {
                                            [photoView photoDidFinishLoadWithImage:image error:error];
                                            if (image == nil) {
                                                [photoView cancelImageLoadingIfFailedTimesExceedsMaxRetryCount];
                                            } else {
                                                [photoView resetCurrentRetryCount];
                                            }
                                        }];
                                } else {
                                    [photoView photoDidFinishLoadWithImage:image error:error];
                                    [photoView resetCurrentRetryCount];
                                }
                            }];
        } else {
            [wview sd_setImageWithURL:photo.url
                placeholderImage:photo.placeholder
                options:SDWebImageRetryFailed | SDWebImageLowPriority
                progress:^(NSInteger receivedSize, NSInteger expectedSize) {
                    if (receivedSize > kMinProgress) {
                        loading.progress = (float)receivedSize / expectedSize;
                    }
                }
                completed:^(UIImage *image, NSError *error, SDImageCacheType cacheType, NSURL *imageURL) {
                    [photoView photoDidFinishLoadWithImage:image error:error];
                
                    if (image == nil) {
                        [photoView cancelImageLoadingIfFailedTimesExceedsMaxRetryCount];
                    } else {
                        [photoView resetCurrentRetryCount];
                    }
                }];
        }
    }
}

- (void)resetCurrentRetryCount {
    self.currentRetryCount = 0;
}

- (void)cancelImageLoadingIfFailedTimesExceedsMaxRetryCount {
    self.currentRetryCount += 1;
    if (self.maxRetryCount < self.currentRetryCount) {
        self.currentRetryCount = 0;
        [self.imageView sd_cancelCurrentImageLoad];
        [_photoLoadingView removeFromSuperview];
    }
}

#pragma mark 加载完毕

- (void)photoDidFinishLoadWithImage:(UIImage *)image error:(NSError *)error {
    if (image) {
        self.scrollEnabled = YES;
        _imageView.contentMode = UIViewContentModeScaleAspectFit;
        _photo.image = image;
        // 如果有用到进度条下载 才去设置图片加载状态,
        // 如果srcImageView没有成功显示图片，则把对应图片传递出去，之前用imy_setImageUrl会再写一次内存缓存，遇到大尺寸图片时，会占用较高内存
        // 只处理 UIImage 的情况，不处理 FLAnimationImage
        if (_photo.srcImageView.sd_imageURL &&
            (_photo.srcImageView.image == _photo.srcImageView.imy_placeholderImage || !_photo.srcImageView.image) &&
            [image isKindOfClass:UIImage.class]) {
            CGSize scaleSize = _photo.srcImageView.frame.size;
            if (scaleSize.width > 1 && scaleSize.height > 1) {
                CGFloat scale = [UIScreen mainScreen].scale;
                scale = scale * scaleSize.width/image.size.width;
                if (scale < 0.2) {
                    scale = 0.2;
                }
                if (scale >= 1) {
                    // 无需缩放，直接原图展示
                    _photo.srcImageView.image = image;
                } else {
                    // 需要缩放，放到异步，避免卡顿
                    imy_asyncBlock(^{
                        UIImage *scaleImage = [image imy_scaledImageByFactor:scale];
                        imy_asyncMainBlock(^{
                            // 如果原imageView 还未下载成功，才赋值
                            if ((self.photo.srcImageView.image == self.photo.srcImageView.imy_placeholderImage || !self.photo.srcImageView.image)) {
                                self.photo.srcImageView.image = scaleImage;
                            }
                        });
                    });
                }
            }
        }

        [_photoLoadingView removeFromSuperview];

        if ([self.photoViewDelegate respondsToSelector:@selector(photoViewImageFinishLoad:)]) {
            [self.photoViewDelegate photoViewImageFinishLoad:self];
        }
    } else {
//        CGFloat width = SCREEN_WIDTH;
//        self.imageView.imy_size = CGSizeMake(width, width);
        _imageView.contentMode = UIViewContentModeScaleAspectFill;
        if(self.overseaFailLoad){
            _imageView.image = [UIImage imy_imageFromColor:[UIColor clearColor] andSize:CGSizeMake(10, 10) opaque:YES]; // 不展示占位灰底图
            [_photoLoadingView showRetry];
            [_photoLoadingView.retryBtn addTarget:self action:@selector(photoStartLoad) forControlEvents:UIControlEventTouchUpInside];
        } else if (_photo.isAvatar) {
            [_photoLoadingView removeFromSuperview];
            [_photoLoadingView showFailure:NO];
        } else {
            [_photoLoadingView removeFromSuperview];
            if (error || ![IMYNetState networkEnable]) {
                [_photoLoadingView showFailure];
            }
        }
        if ([self.photoViewDelegate respondsToSelector:@selector(photoViewImageFail:)]) {
            [self.photoViewDelegate photoViewImageFail:self];
        }
    }
    if (!error) {
        // 设置缩放比例
        [self adjustFrame];
    }
}

#pragma mark 调整frame

- (void)adjustFrame {
    if (_imageView.image == nil) {
        return;
    }

    // 基本尺寸参数
    CGSize boundsSize = self.bounds.size;
    CGFloat boundsWidth = boundsSize.width;
    CGFloat boundsHeight = boundsSize.height;
    if (boundsWidth == 0 || boundsHeight == 0) {
        boundsSize = UIScreen.mainScreen.bounds.size;
        boundsWidth = boundsSize.width;
        boundsHeight = boundsSize.height;
    }
    
    CGSize imageSize = _imageView.image.size;
    CGFloat imageWidth = imageSize.width;
    CGFloat imageHeight = imageSize.height;
    if (imageWidth == 0 || imageHeight == 0) {
        imageWidth = boundsWidth;
        imageHeight = boundsHeight;
    }
    CGRect imageFrame = CGRectMake(0, 0, boundsWidth, imageHeight * boundsWidth / imageWidth);
    // y值
    if (imageFrame.size.height < boundsHeight) {
        imageFrame.origin.y = floorf((boundsHeight - imageFrame.size.height) / 2.0);
    } else {
        imageFrame.origin.y = 0;
    }
    // 是否特殊尺寸，只对图片生效
    BOOL needSpecialHandle = NO;
    UIView *frameView = nil;
    
    // 计算具体显示区域
    if (_photo.assetModel.assetType == IMYAssetTypeVideo) {
        // 视频模式，不支持缩放
        self.maximumZoomScale = 1.0;
        self.minimumZoomScale = 1.0;
        self.zoomScale = 1.0;
        // 限制视频显示尺寸
        CGFloat topBarHeight = SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT;
        CGFloat bottomBarHeight = 50 + SCREEN_TABBAR_SAFEBOTTOM_MARGIN;
        if (self.showFullScreenVideo) {
            topBarHeight = 0;
            bottomBarHeight = 0;
            self.videoCloseBtn.hidden = NO;
        }
        CGFloat maxVideoHeight = boundsHeight - topBarHeight - bottomBarHeight;
        // 尺寸超大的进行缩放
        if (imageFrame.size.height > maxVideoHeight) {
            // 固定大小
            imageFrame.origin.y = topBarHeight;
            imageFrame.size.height = maxVideoHeight;
        }
        _videoPlayer.frame = imageFrame;
    } else {
        // 图片模式
        self.maximumZoomScale = 4.0;
        self.minimumZoomScale = 1.0;
        self.zoomScale = 1.0;
        // 内容尺寸
        self.contentSize = imageFrame.size;
        // 计算特殊尺寸的情况
        needSpecialHandle = [self needSpecialHandle];
        frameView = _photo.srcSuperView ?: _photo.srcImageView;
        CGSize imageUrlSize = [self getImageSizeFromUrl];
        if (_photo.srcImageView && !CGSizeEqualToSize(CGSizeZero, _photo.srcImageView.frame.size) && !CGSizeEqualToSize(CGSizeZero, imageUrlSize)) {
            CGSize originSize = _photo.srcImageView.frame.size;
            /// 按目标图放大，比较height偏差，看比例是不是一致，20只是个大概数，这样放大动画会比较准、自然
            originSize = CGSizeMake(imageUrlSize.width, 1.f*originSize.height*imageUrlSize.width/originSize.width);
            if (fabs(originSize.height - imageUrlSize.height) > 20) {
                /// 尺寸偏差较大就按图片尺寸放大
                needSpecialHandle = YES;
            }
        }
        if (needSpecialHandle) {
            if (!CGSizeEqualToSize(CGSizeZero, imageUrlSize)) {
                needSpecialHandle = NO;
                imageFrame.size = CGSizeMake(self.imy_width, ceilf(self.imy_width*imageUrlSize.height/imageUrlSize.width));
                if (imageFrame.size.height < boundsHeight) {
                     imageFrame.origin.y = floorf((boundsHeight - imageFrame.size.height) / 2.0);
                 } else {
                     imageFrame.origin.y = 0;
                 }
                _imageView.clipsToBounds = YES;
            } else {
                CGSize frameViewSize = frameView.imy_size;
                if (CGSizeEqualToSize(CGSizeZero, frameViewSize)) {
                    // 取不到frameView的时候就以默认图的大小作为显示大小
                    frameViewSize = _imageView.image.size;
                }
                imageFrame.size = frameViewSize;
                imageFrame.origin.x = (self.imy_width - frameViewSize.width) / 2;
                imageFrame.origin.y = (self.imy_height - frameViewSize.height) / 2;
            }
        }
    }
    if (_photo.firstShow || needSpecialHandle) { // 第一次显示的图片
        _photo.firstShow = NO;                   // 已经显示过了
        if ((_photo.assetModel || _photo.albumUrl) || _photo.disableAnimation || _photo.isLongImg) {
            _imageView.frame = imageFrame;
        } else if (needSpecialHandle) {
            _imageView.frame = imageFrame;
            [self photoStartLoad];
        }
        else {
            if (frameView) {
                _imageView.frame = [frameView convertRect:frameView.bounds toView:nil];
            } else {
                _imageView.frame = CGRectInset(imageFrame, 20, 20);
            }
            //            _imageView.imy_top += 20;
            if (_photo.srcImageView) {
                _initialFrame = _imageView.frame;
                _imageView.contentMode = self.photo.srcImageView.contentMode;
                _imageView.clipsToBounds = self.photo.srcImageView.clipsToBounds;
            }
            self.presentingAnimation = YES;
            [UIView animateWithDuration:0.3
                             animations:^{
                _imageView.frame = imageFrame;
            }
                             completion:^(BOOL finished) {
                // 设置底部的小图片
                //            _photo.srcImageView.image = _photo.placeholder;
                self.presentingAnimation = NO;
                [self photoStartLoad];
            }];
        }
    } else if (!self.presentingAnimation) {
        //正在做动画就不直接设置
        _imageView.frame = imageFrame;
    }
}

- (void)fixFrame {
    if (_imageView.image == nil) {
        return;
    }
}

- (BOOL)needSpecialHandle {
    //特殊处理，当只有小图的时候，图片显示就是居中，并且不拉伸图片
    if (_photo.sy_placeholder) {
        //有底图的就用底图加载
        return NO;
    }
    BOOL needSpecialHandle = NO;
    if ([_photo.url.absoluteString length]) {
        UIImage *image = nil;
        if (_photo.isAvatar) {
            NSURL *newURL = nil;
            if (newURL) {
                _photo.url = newURL;
            }
            if (image) {
                needSpecialHandle = NO;
            }
        }
        if (_photo.image) {
            needSpecialHandle = NO;
        } else if (!image) {
            needSpecialHandle = [[SDImageCache sharedImageCache] imageFromMemoryCacheForKey:_photo.url.absoluteString] == nil;
            //双核机器直接再读一次磁盘缓存
            if (needSpecialHandle && [UIDevice imy_dualCore]) {
                needSpecialHandle = [[SDImageCache sharedImageCache] imageFromDiskCacheForKey:_photo.url.absoluteString] == nil;
            }
        }
    }
    return needSpecialHandle;
}

/// 获取图片尺寸
- (CGSize)getImageSizeFromUrl {
    NSError *error;
    NSRegularExpression *regular = [NSRegularExpression regularExpressionWithPattern:kImageSizeUrlRegex options:0 error:&error];
    if (!error) {
        NSString *imageUrl = self.photo.url.absoluteString;
        NSTextCheckingResult *match = [regular firstMatchInString:imageUrl options:0 range:NSMakeRange(0, [imageUrl length])];
        if (match) {
            NSString *result = [imageUrl substringWithRange:match.range];
            NSArray *array = [result componentsSeparatedByString:@"_"];
            return CGSizeMake([array[1] integerValue], [array[2] integerValue]);
        }
    }
    return CGSizeZero;
}

#pragma mark - UIScrollViewDelegate

- (UIView *)viewForZoomingInScrollView:(UIScrollView *)scrollView {
    if (self.photo.image) {
        // 没有图片时不做放大的交互
        return _imageView;
    } else {
        return nil;
    }
}

- (void)scrollViewDidZoom:(UIScrollView *)scrollView {
    self.imageView.center = [self centerOfScrollViewContent:scrollView];
}

- (CGPoint)centerOfScrollViewContent:(UIScrollView *)scrollView {
    CGFloat offsetX = (scrollView.bounds.size.width > scrollView.contentSize.width) ? (scrollView.bounds.size.width - scrollView.contentSize.width) * 0.5 : 0;
    CGFloat offsetY = (scrollView.bounds.size.height > scrollView.contentSize.height) ? (scrollView.bounds.size.height - scrollView.contentSize.height) * 0.5 : 0;
    CGPoint actualCenter = CGPointMake(scrollView.contentSize.width * 0.5 + offsetX, scrollView.contentSize.height * 0.5 + offsetY);
    return actualCenter;
}

#pragma mark - 手势处理

- (void)handleSingleTap:(UITapGestureRecognizer *)tap {
    _doubleTap = NO;
    if (self.overseaFailLoad) {
        return; // 海外版不处理单击手势
    }
    [self performSelector:@selector(hide) withObject:nil afterDelay:0.3];
}

- (void)hide {
    if (_doubleTap) {
        return;
    }
    // 本地相册放大模式
    BOOL isPhotoPicker = !_photo.isBrowser && (_photo.assetModel || _photo.albumUrl);
    if (isPhotoPicker || _photo.disableAnimation || _ignoreDidEndZoomEvent) {
        // 通知代理
        if ([self.photoViewDelegate respondsToSelector:@selector(photoViewSingleTap:)]) {
            [self.photoViewDelegate photoViewSingleTap:self];
        }
    } else {
        disableLayoutSubvies = YES;
        // 移除进度条
        [_photoLoadingView removeFromSuperview];
        CGFloat duration = 0.30;
        // 先把之前的小图隐藏了。等动画完了再显示出来
//        self.photo.srcImageView.hidden = YES;
        if (_zoom || self.zoomScale != self.minimumZoomScale) {
            [self setZoomScale:self.minimumZoomScale animated:YES];
        }

        // 通知代理,由容器自己处理dismiss动画
        if ([self.photoViewDelegate respondsToSelector:@selector(photoViewSingleTap:)]) {
            [self.photoViewDelegate photoViewSingleTap:self];
        }
    }
}

- (void)reset {
    _imageView.contentMode = _photo.srcImageView.contentMode;
}

-(void)closeVideoView:(UIButton *)sender{
    imy_asyncMainBlock(^{
        // 通知代理
        if ([self.photoViewDelegate respondsToSelector:@selector(photoViewDidEndZoom:)]) {
            [self.photoViewDelegate photoViewDidEndZoom:self];
        }
    });
}

/// 双击进行大图和原图切换
/// @param tap tap description
// TODO: 处在大图下要禁用拖动消失
- (void)handleDoubleTap:(UITapGestureRecognizer *)tap {
    [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(hide) object:nil];
    _doubleTap = YES;

    if (self.zoomScale == self.maximumZoomScale || _zoom) {
        // Zoom out
        _zoom = NO;
        [self setZoomScale:self.minimumZoomScale animated:YES];
    } else {
        _zoom = YES;
        CGPoint location = [tap locationInView:self];
        CGFloat wh       = 1.0;
        CGRect aRect = [self getRectWithScale:[self fixScreenZoomSize] andCenter:location];
        [self zoomToRect:aRect animated:YES];
    }
}

- (CGRect)frameWithWidth:(CGFloat)width height:(CGFloat)height center:(CGPoint)center {
    CGFloat x = center.x - width * 0.5;
    CGFloat y = center.y - height * 0.5;
    return CGRectMake(x, y, width, height);
}

/** 计算点击点所在区域frame */
- (CGRect)getRectWithScale:(CGFloat)scale andCenter:(CGPoint)center{
    CGRect newRect = CGRectZero;
    newRect.size.width =  self.frame.size.width/scale;
    newRect.size.height = self.frame.size.height/scale;
    newRect.origin.x = center.x - newRect.size.width * 0.5;
    newRect.origin.y = center.y - newRect.size.height * 0.5;

    return newRect;
}

//计算双击放大至屏幕高度的scale
- (CGFloat)fixScreenZoomSize {
    // 基本尺寸参数
    CGSize boundsSize = self.bounds.size;
    CGFloat boundsWidth = boundsSize.width;
    CGFloat boundsHeight = boundsSize.height;
    if (boundsWidth == 0 || boundsHeight == 0) {
        boundsSize = UIScreen.mainScreen.bounds.size;
        boundsWidth = boundsSize.width;
        boundsHeight = boundsSize.height;
    }
    
    CGSize imageSize = _imageView.image.size;
    CGFloat imageWidth = imageSize.width;
    CGFloat imageHeight = imageSize.height;
    if (imageWidth == 0 || imageHeight == 0) {
        imageWidth = boundsWidth;
        imageHeight = boundsHeight;
    }
    
    CGSize imageVSize = CGSizeMake(boundsWidth, imageHeight * boundsWidth / imageWidth);
    // 内容尺寸

    // 设置伸缩比例
    CGFloat maxScale = 2.0;
    CGFloat scaleH = boundsHeight / imageVSize.height;
    CGFloat scaleW = boundsWidth / imageVSize.width;
    maxScale = MAX(MAX(scaleH, scaleW), maxScale);
    return maxScale;
}

- (void)longPress:(UILongPressGestureRecognizer *)longPressGestureRecognizer {
    if (_photoViewDelegate && [_photoViewDelegate respondsToSelector:@selector(photoViewShouldLongPress:)]) {
        [_photoViewDelegate photoViewShouldLongPress:self];
    }
}

- (void)dealloc {
    [_imageView sd_cancelCurrentImageLoad];
}

- (void)resetImageNilForAlbum {
    if (self.photo.assetModel || self.photo.albumUrl) {
        self.photo.image = nil;
    }
}
@end
