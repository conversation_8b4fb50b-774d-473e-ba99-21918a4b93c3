//
//  IMYAssetsCollectionViewCell.h
//  IMYViewKit
//
//  Created by <PERSON><PERSON>l<PERSON> on 15/6/3.
//  Copyright (c) 2015年 IMY. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "IMYImageSelectButton.h"
#import "IMYAssetModel.h"

@interface IMYAssetsCollectionViewCell : UICollectionViewCell

@property (nonatomic, strong) UIImageView *imageView;

@property (nonatomic, assign) BOOL showsOverlayViewWhenSelected;
@property (nonatomic, strong) IMYImageSelectButton *btn_select;
@property (nonatomic, assign) BOOL btnSelectHidden;

@property (nonatomic, assign, readonly) IMYAssetType assetType;

- (void)configUIWithModel:(IMYAssetModel *)asset;

///  是否显示不可选择蒙版
- (void)showDisableCover:(BOOL)show;
- (BOOL)isShowDisableCovered;

@end
