//
//  IMYImagePickerGroupCell.m
//  IMYViewKit
//
//  Created by <PERSON>.lin on 15/6/3.
//  Copyright (c) 2015年 IMY. All rights reserved.
//

#import "IMYImagePickerGroupCell.h"
// Views
#import "IMYImagePickerThumbnailView.h"
#import "IMYAssetsGroupModel.h"
#import "IMYAssetModel.h"
#import "IMYPublic.h"

@interface IMYImagePickerGroupCell ()

@property (nonatomic, strong) UIImageView *topImageView;
@property (nonatomic, strong) UIImageView *middleImageView;
@property (nonatomic, strong) UIImageView *bottomImageView;
@property (nonatomic, strong) UIView *imageContainerView;


@property (nonatomic, strong) IMYImagePickerThumbnailView *thumbnailView;
@property (nonatomic, strong) UILabel *nameLabel;
@property (nonatomic, strong) UILabel *countLabel;

@end

@implementation IMYImagePickerGroupCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    // Cell settings
    self.accessoryType = UITableViewCellAccessoryDisclosureIndicator;
    
    // Create thumbnail view
    self.imageContainerView = [[UIView alloc] initWithFrame:CGRectMake(8, 4, 70, 74)];
    self.imageContainerView.autoresizingMask = UIViewAutoresizingFlexibleRightMargin | UIViewAutoresizingFlexibleBottomMargin;
    [self.contentView addSubview:self.imageContainerView];

    
    // Create name label
    UILabel *nameLabel = [[UILabel alloc] initWithFrame:CGRectMake(8 + 70 + 18, 22, 180, 21)];
    nameLabel.font = [UIFont systemFontOfSize:17];
    nameLabel.textColor = [UIColor blackColor];
    nameLabel.autoresizingMask = UIViewAutoresizingFlexibleRightMargin | UIViewAutoresizingFlexibleBottomMargin | UIViewAutoresizingFlexibleWidth;
    
    [self.contentView addSubview:nameLabel];
    self.nameLabel = nameLabel;
    
    // Create count label
    UILabel *countLabel = [[UILabel alloc] initWithFrame:CGRectMake(8 + 70 + 18, 46, 180, 15)];
    countLabel.font = [UIFont systemFontOfSize:12];
    countLabel.textColor = [UIColor blackColor];
    countLabel.autoresizingMask = UIViewAutoresizingFlexibleRightMargin | UIViewAutoresizingFlexibleBottomMargin | UIViewAutoresizingFlexibleWidth;
    
    [self.contentView addSubview:countLabel];
    self.countLabel = countLabel;
    
    // to scrren width And RTL
    if (IMYISRTL) {
        self.imy_width = SCREEN_WIDTH;
        [self.contentView imy_flippedHorizontallySubviews];
    }
}

- (void)prepareForReuse {
    [super prepareForReuse];
    _middleImageView.hidden = YES;
    _bottomImageView.hidden = YES;
}

#pragma mark - Accessors

- (void)setPhAssetsGroup:(IMYAssetsGroupModel *)phAssetsGroup {
    _phAssetsGroup = phAssetsGroup;
    self.nameLabel.text = phAssetsGroup.title;
    self.countLabel.text = [NSString stringWithFormat:@"%@", @(phAssetsGroup.totolCount)];
    if (phAssetsGroup.totolCount <= 0) {
        return;
    }
    [NSObject imy_asyncBlockExecuteBlock:^{
        for (int i = 1; i <= MIN(phAssetsGroup.totolCount, 3); i++) {
            UIImage *posterImage = [phAssetsGroup synRequestImageAtIndex:phAssetsGroup.totolCount-i targetSize:CGSizeMake(70, 70)];
            imy_asyncMainBlock(^{
                switch (i) {
                    case 1: {
                            self.topImageView.image = posterImage;
                    }
                        break;
                    case 2: {
                            self.middleImageView.hidden = NO;
                            self.middleImageView.image = posterImage;
                    }
                        break;
                    case 3: {
                            self.bottomImageView.hidden = NO;
                            self.bottomImageView.image = posterImage;
                    }
                        break;
                    default:
                        break;
                }
            });
        }
    }];
}

- (IMYImagePickerThumbnailView *)thumbnailView {
    if (!_thumbnailView) {
        _thumbnailView = [[IMYImagePickerThumbnailView alloc] initWithFrame:self.imageContainerView.bounds];
        _thumbnailView.autoresizingMask = UIViewAutoresizingFlexibleRightMargin | UIViewAutoresizingFlexibleBottomMargin;
        [self.imageContainerView addSubview:_thumbnailView];
    }
    return _thumbnailView;
}

- (UIImageView *)topImageView {
    if (!_topImageView) {
        _topImageView = [[UIImageView alloc] initWithFrame:CGRectMake(0, 4.0, 70.0, 70.0)];
        _topImageView.contentMode = UIViewContentModeScaleAspectFill;
        _topImageView.clipsToBounds = YES;
        [self.imageContainerView insertSubview:_topImageView aboveSubview:self.middleImageView];
    }
    return _topImageView;
}

- (UIImageView *)middleImageView {
    if (!_middleImageView) {
        _middleImageView = [[UIImageView alloc] initWithFrame:CGRectMake(2.0, 2.0, 66.0, 66.0)];
        _middleImageView.hidden = YES;
        _middleImageView.contentMode = UIViewContentModeScaleAspectFill;
        _middleImageView.clipsToBounds = YES;
        [self.imageContainerView insertSubview:_middleImageView aboveSubview:self.bottomImageView];
    }
    return _middleImageView;
}

- (UIImageView *)bottomImageView {
    if (!_bottomImageView) {
        _bottomImageView = [[UIImageView alloc] initWithFrame:CGRectMake(4.0, 0, 62.0, 62.0)];
        _bottomImageView.hidden = YES;
        _bottomImageView.contentMode = UIViewContentModeScaleAspectFill;
        _bottomImageView.clipsToBounds = YES;
        [self.imageContainerView insertSubview:_bottomImageView belowSubview:self.middleImageView];
    }
    return _bottomImageView;
}



@end
