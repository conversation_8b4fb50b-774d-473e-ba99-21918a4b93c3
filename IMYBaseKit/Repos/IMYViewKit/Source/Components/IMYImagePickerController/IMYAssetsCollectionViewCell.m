//
//  IMYAssetsCollectionViewCell.m
//  IMYViewKit
//
//  Created by <PERSON>.l<PERSON> on 15/6/3.
//  Copyright (c) 2015年 IMY. All rights reserved.
//

#import "IMYAssetsCollectionViewCell.h"
#import "IMYViewKit.h"
#import "IMYAssetModel.h"
#import "IMYiCloudAssetCoverView.h"

@interface IMYAssetsCollectionViewCell ()

@property (nonatomic, strong) UIImage *image;
@property (nonatomic, copy) NSString  *assetIdentifier;
@property (nonatomic, strong) UIView *gifIconView;
@property (nonatomic, strong) UIView *whiteCover;
@property (nonatomic, strong) IMYiCloudAssetCoverView *iCloudAssetCoverView;
@property (nonatomic, strong) UILabel *durationLabel; // 视频时长
@end

@implementation IMYAssetsCollectionViewCell

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];

    if (self) {
        self.showsOverlayViewWhenSelected = YES;
        [self imy_setBackgroundColorForKey:kCK_White_AT];

        // Create a image view
        UIImageView *imageView = [[UIImageView alloc] initWithFrame:self.contentView.bounds];
        imageView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
        imageView.clipsToBounds = YES;
        imageView.contentMode = UIViewContentModeScaleAspectFill;
        [self.contentView addSubview:imageView];
        self.imageView = imageView;
        
        UIView *gifIconView = [UIView new];
        gifIconView.hidden = YES;
        gifIconView.backgroundColor = [[UIColor imy_colorWithHexString:@"#030303"] colorWithAlphaComponent:0.4];
        gifIconView.layer.borderWidth = 1;
        gifIconView.layer.borderColor = [[UIColor whiteColor] colorWithAlphaComponent:0.4].CGColor;
        gifIconView.layer.cornerRadius = 9;
        [self.contentView addSubview:gifIconView];
        self.gifIconView = gifIconView;
        [gifIconView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.contentView).offset(4);
            make.bottom.equalTo(self.contentView).offset(-4);
            make.width.mas_equalTo(28);
            make.height.mas_equalTo(18);
        }];
        
        UILabel *gifLabel = [UILabel new];
        gifLabel.text = @"GIF";
        gifLabel.textColor = [UIColor imy_colorForKey:kCK_White_A];
        gifLabel.font = [UIFont systemFontOfSize:10];
        gifLabel.textAlignment = NSTextAlignmentCenter;
        [gifIconView addSubview:gifLabel];
        [gifLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.mas_equalTo(UIEdgeInsetsZero);
        }];

        UIView *whiteCover = [[UIView alloc] initWithFrame:self.contentView.bounds];
        whiteCover.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
        whiteCover.backgroundColor = [UIColor colorWithWhite:1 alpha:0.7];
        whiteCover.hidden = YES;
        [self.contentView addSubview:whiteCover];
        self.whiteCover = whiteCover;
        
        _durationLabel = [[UILabel alloc] init];
        _durationLabel.font = [UIFont systemFontOfSize:11];
        _durationLabel.textAlignment = NSTextAlignmentRight;
        [_durationLabel imy_setTextColorForKey:kCK_White_A];
        [self.contentView addSubview:_durationLabel];
        [_durationLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(self.contentView).mas_offset(-5);
            make.bottom.equalTo(self.contentView).mas_offset(-6);
            make.left.equalTo(self.contentView).mas_offset(5);
            make.height.mas_equalTo(12);
        }];
        
        if (IMYPublicAppHelper.isWenzhenYisheng) {
            _btn_select = [[IMYImageSelectButton alloc] initWithFrame:CGRectMake(0, 3, 24, 24) normalImage:@"ys_icon_choose.png" selectedImage:@"ys_icon_choose_selected.png"];
        } else {
            _btn_select = [[IMYImageSelectButton alloc] initWithFrame:CGRectMake(0, 3, 24, 24) normalImage:@"asset_icon_choose.png" selectedImage:@"asset_icon_choose_selected.png"];
        }
        
        @weakify(self);
        [_btn_select imy_addLanguageLayoutsChangeActionBlock:^(UIView *weakObject) {
            @strongify(self);
            if (IMYISRTL) {
                weakObject.imy_left = 3;
            } else {
                weakObject.imy_right = self.imy_width - 3;
            }
        }];
        
        [self.contentView addSubview:_btn_select];
        
        self.iCloudAssetCoverView = [[IMYiCloudAssetCoverView alloc] initWithFrame:self.contentView.bounds];
        self.iCloudAssetCoverView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
        [self.contentView addSubview:self.iCloudAssetCoverView];

        // 这边需要先设置 Block
        [self.iCloudAssetCoverView setRefreshBlock:^(NSString * _Nonnull identifier, IMYiCloudAssetModel_Status status) {
            @strongify(self);
            if ([identifier isEqualToString:self.assetIdentifier]) {
                [self refreshWithiCloudAssetStatus:status];
            }
        }];
        
//        UIButton *debugButton = [[UIButton alloc] initWithFrame:CGRectMake(0, 0, 30, 30)];
//        debugButton.backgroundColor = [UIColor blueColor];
//        [self.contentView addSubview:debugButton];
//        [[debugButton rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(__kindof UIControl * _Nullable x) {
//            @strongify(self);
//            IMYAssetModel *tempAssetModel = [[IMYAssetModel alloc] initWithIdentifier:self.assetIdentifier];
//
//            NSLog(@"[debug icloud photo][查看 icloudAseetModel 数据][icloudAseetModel : %@]", tempAssetModel.iCloudAssetModel.imy_jsonString);
//
//            [[IMYiCloudAssetManager manager] localEnableForAsset:tempAssetModel result:^(IMYAssetModel * _Nullable currentAssetModel, BOOL isLocalEnable) {
//                NSLog(@"[debug icloud photo][查看 localEnableForAsset 数据][localEnable : %@]", @(isLocalEnable));
//            }];
//        }];
    }

    return self;
}

- (void)setSelected:(BOOL)selected {
    [super setSelected:selected];
}

- (void)configUIWithModel:(IMYAssetModel *)asset {
    // 判断是否相同ID
    const BOOL hasEqualId = [_assetIdentifier isEqualToString:asset.identifier];
    _assetType = asset.assetType;
    _assetIdentifier = asset.identifier;
    _gifIconView.hidden = !(asset.assetType == IMYAssetTypeImage && asset.assetSubType == IMYAssetSubTypeGIF);
    // 跟旧identify 不相等，才需要加载 image 和 实时刷新UI
    _durationLabel.text = asset.duration;
    if (!hasEqualId) {
        @weakify(self);
        [asset requestThumbnailImageWithSize:self.imageView.frame.size completion:^(UIImage * _Nonnull result, NSDictionary<NSString *,id> * _Nonnull info) {
            @strongify(self);
            if ([self.assetIdentifier isEqualToString:asset.identifier]) {
                self.imageView.image = result;
            }
            // 如果不相等的话，不要设为nil，否则会导致很多图片都是nil
        }];
        // 刷新按钮状态
        [self refreshWithiCloudAssetStatus:asset.iCloudAssetModel.status];
    }
    
    // 设置 iCloud 状态
    [_iCloudAssetCoverView setAssetModel:asset];
}

- (void)refreshWithiCloudAssetStatus:(IMYiCloudAssetModel_Status)status {
    if (status == IMYiCloudAssetModel_Status_inDownload || status == IMYiCloudAssetModel_Status_inCloud) {
        self.btn_select.hidden = YES;
    } else {
        self.btn_select.hidden = self.btnSelectHidden;
    }
}

- (void)showDisableCover:(BOOL)show {
    self.whiteCover.hidden = !show;
}

- (BOOL)isShowDisableCovered {
    return !self.whiteCover.hidden;
}



@end
