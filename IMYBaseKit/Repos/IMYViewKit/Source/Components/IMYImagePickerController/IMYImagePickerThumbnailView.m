//
//  IMYImagePickerThumbnailView.m
//  IMYViewKit
//
//  Created by <PERSON><PERSON>lin on 15/6/3.
//  Copyright (c) 2015年 IMY. All rights reserved.
//

#import "IMYImagePickerThumbnailView.h"

#import "IMYAssetModel.h"
#import "IMYAssetsGroupModel.h"

@interface IMYImagePickerThumbnailView ()

@property (nonatomic, copy) NSArray *thumbnailImages;
@end

@implementation IMYImagePickerThumbnailView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];

    if (self) {
        self.backgroundColor = [UIColor clearColor];
    }

    return self;
}

- (CGSize)sizeThatFits:(CGSize)size {
    return CGSizeMake(70.0, 74.0);
}

- (void)drawRect:(CGRect)rect {
    [super drawRect:rect];

    CGContextRef context = UIGraphicsGetCurrentContext();
    CGContextSetRGBFillColor(context, 1.0, 1.0, 1.0, 1.0);

    if (self.thumbnailImages.count == 3) {
        UIImage *thumbnailImage = self.thumbnailImages[2];

        CGRect thumbnailImageRect = CGRectMake(4.0, 0, 62.0, 62.0);
        CGContextFillRect(context, thumbnailImageRect);
        [thumbnailImage drawInRect:CGRectInset(thumbnailImageRect, 0.5, 0.5)];
    }

    if (self.thumbnailImages.count >= 2) {
        UIImage *thumbnailImage = self.thumbnailImages[1];

        CGRect thumbnailImageRect = CGRectMake(2.0, 2.0, 66.0, 66.0);
        CGContextFillRect(context, thumbnailImageRect);
        [thumbnailImage drawInRect:CGRectInset(thumbnailImageRect, 0.5, 0.5)];
    }

    UIImage *thumbnailImage = self.thumbnailImages[0];

    CGRect thumbnailImageRect = CGRectMake(0, 4.0, 70.0, 70.0);
    CGContextFillRect(context, thumbnailImageRect);
    [thumbnailImage drawInRect:CGRectInset(thumbnailImageRect, 0.5, 0.5)];
}


#pragma mark - Accessors

- (void)setAssetsGroup:(IMYAssetsGroupModel *)assetsGroup {
    _assetsGroup = assetsGroup;
    NSMutableArray *thumbnailImages = [NSMutableArray array];
    [assetsGroup resetAssetsFetchLimit: MIN(3, assetsGroup.totolCount)];
    [assetsGroup enumerateAssetsWithOptions:(IMYAssetsSortTypeReverse) usingBlock:^(IMYAssetModel * _Nonnull asset) {
        if (asset != nil && asset.originImage != nil) {
            [thumbnailImages addObject:asset.originImage];
        }
    }];
    self.thumbnailImages = [thumbnailImages copy];
}

@end
