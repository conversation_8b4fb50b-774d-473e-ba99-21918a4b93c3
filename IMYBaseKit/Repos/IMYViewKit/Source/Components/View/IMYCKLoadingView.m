//
//  IMYCKLoadingView.m
//  IMYCommonKit
//
//  Created by 林云峰 on 2021/5/13.
//

#import "IMYCKLoadingView.h"
#import "IMYViewKit.h"
@interface IMYCKLoadingView () <IMYTimerRuningProtocol>
/// 做光动画的线
@property (nonatomic, strong) UIImageView *loadingLine;
/// 限定光条动画范围的view
@property (nonatomic, strong) UIView *loadingContainer;
@property (nonatomic, assign) IMYCKLoadingType type;

@end

@implementation IMYCKLoadingView

- (instancetype)initWithtype:(IMYCKLoadingType)type
{
    self = [super init];
    if (self) {
        [self imy_setBackgroundColorForKey:kCK_White_AN];
        self.type = type;
        [self _initMyself];
    }
    return self;
}

- (instancetype)initWithLoadingView:(UIView *)loadingView {
    self = [super init];
    if (self) {
        self.clipsToBounds = YES;
        [self imy_setBackgroundColorForKey:kCK_White_AN];
        [self addSubview:loadingView];
        self.frame = loadingView.frame;
        
        self.loadingContainer = [[UIView alloc] initWithFrame:CGRectMake(12, 0, SCREEN_WIDTH - 24, loadingView.imy_height)];
        self.loadingContainer.clipsToBounds = YES;
        [self addSubview:self.loadingContainer];
        if (loadingView.layer.cornerRadius > 0) {
            self.loadingContainer.layer.cornerRadius = loadingView.layer.cornerRadius;
        }

        self.loadingLine = [[UIImageView alloc] initWithFrame:CGRectMake(-35, 0, 150, loadingView.imy_height)];
        self.loadingLine.tag = 9999;
        
        [self.loadingLine imy_addThemeActionBlock:^(UIImageView *weakObject) {
            UIImage *lineImage = [UIImage imy_imageForKey:@"refresh_preloading_light"];
            if ([IMYPublicAppHelper shareAppHelper].isNight) {
                lineImage = [lineImage imy_imageWithTintColor:IMY_COLOR_KEY(kCK_White_AN)];
            }
            weakObject.image = lineImage;
        } forKey:@"setImage"];
        
        [self.loadingContainer addSubview:self.loadingLine];
        
        /// 有时动画会丢失，没查明原因，现在轮询处理
        [[IMYTimerHelper timerHelperWithInterval:0.5] addTimerForObject:self];
    }
    return self;
}

- (void)_initMyself {
    self.clipsToBounds = YES;
    UIView *loadingView = [IMYCKLoadingView loadingViewWithType:self.type];
    loadingView.tag = 'img';
    [self addSubview:loadingView];
    self.frame = loadingView.frame;
    
    self.loadingContainer = [[UIView alloc] initWithFrame:CGRectMake(12, 0, SCREEN_WIDTH - 24, loadingView.imy_height)];
    self.loadingContainer.clipsToBounds = YES;
    [self addSubview:self.loadingContainer];
    if (loadingView.layer.cornerRadius > 0) {
        self.loadingContainer.layer.cornerRadius = loadingView.layer.cornerRadius;
    }
    
    self.loadingLine = [[UIImageView alloc] initWithFrame:CGRectMake(-35, 0, 150, loadingView.imy_height)];
    self.loadingLine.tag = 9999;
    [self.loadingLine imy_addThemeActionBlock:^(UIImageView *weakObject) {
        UIImage *lineImage = [UIImage imy_imageForKey:@"refresh_preloading_light"];
        if ([IMYPublicAppHelper shareAppHelper].isNight) {
            lineImage = [lineImage imy_imageWithTintColor:IMY_COLOR_KEY(kCK_White_AN)];
        }
        weakObject.image = lineImage;
    } forKey:@"setImage"];
    
    if (self.type == IMYCKLoadingTodaySearchGuessYou) {
        self.loadingLine.image = [self.loadingLine.image imy_imageWithTintColor:IMY_COLOR_KEY(kIMY_BG)];
        self.loadingLine.alpha = 0.6;
    }
    [self.loadingContainer addSubview:self.loadingLine];
    
    if (self.type == IMYCKLoadingMiniVideo) {
        [self imy_setBackgroundColorForKey:kIMY_BG];
    }
    /// 有时动画会丢失，没查明原因，现在轮询处理
    [[IMYTimerHelper timerHelperWithInterval:0.5] addTimerForObject:self];
}

- (void)updateAnimationContainerWithFrame:(CGRect)frame {
    self.loadingContainer.frame = frame;
    self.loadingLine.imy_height = frame.size.height;
}

- (UIView *)mainLoadingView {
    return [self viewWithTag:'img'];
}

- (void)startLoadingAnimation {
    if (!self.window) {
        return;
    }
    /// 这里做下延迟，避免在viewDidload就开始做动画，导致动画失效
    if (![self.loadingLine.layer animationForKey:@"position"]) {
        [NSObject imy_asyncBlock:^{
            if (IMYISRTL) {
                self.loadingLine.imy_left = self.imy_width - 24 - 30;
                [UIView animateWithDuration:1 delay:0.01 options:UIViewAnimationCurveLinear|UIViewAnimationOptionRepeat animations:^{
                    self.loadingLine.imy_left = -120;
                } completion:nil];
            } else {
                self.loadingLine.imy_left = -120;
                [UIView animateWithDuration:1 delay:0.01 options:UIViewAnimationCurveLinear|UIViewAnimationOptionRepeat animations:^{
                    self.loadingLine.imy_left = self.imy_width - 24 - 30;
                } completion:nil];
            }

        } onLevel:IMYQueueLevelMain afterSecond:0.1 forKey:@"loadingViewAniamtionKey"];
    }
//    [self.loadingLine.layer removeAllAnimations];
}

- (void)willMoveToWindow:(UIWindow *)newWindow {
    [super willMoveToWindow:newWindow];
    if (newWindow) {
        [self startLoadingAnimation];
        [[IMYTimerHelper timerHelperWithInterval:0.5] addTimerForObject:self];
    } else {
        [[IMYTimerHelper timerHelperWithInterval:0.5] removeTimerForObject:self];
    }
}

- (void)imy_timerRuning {
    [self startLoadingAnimation];
}

+ (UIView *)loadingViewWithType:(IMYCKLoadingType)type {
    if (type == IMYCKLoadingDetail) {
        return [self detailLoading];
    }
    if (type == IMYCKLoadingComment) {
        return [self commentLoading];
    }
    if (type == IMYCKLoadingTopicDetail) {
        return [self topicDetailLoading];
    }
    if (type == IMYCKLoadingFeeds) {
        return [self feedsLoading];
    }
    if (type == IMYCKLoadingTips) {
        return [self tipsLoading];
    }
    if (type == IMYCKLoadingMiniVideo) {
        return [self miniVideoFeedsLoading];
    }
    if (type == IMYCKLoadingTodayReport) {
        return [self todayReportLoading];
    }
    if (type == IMYCKLoadingTodayReportSuggest) {
        return [self todayReportSuggestLoading:(SCREEN_WIDTH - 12 * 2)];
    }
    if (type == IMYCKLoadingTodayReportSuggestImmerse) {
        return [self todayReportSuggestLoading:(SCREEN_WIDTH - 24 * 2)];
    }
    if (type == IMYCKLoadingTodayReportTest) {
        return [self todayReportTestLoading];
    }
    if (type == IMYCKLoadingTodaySearchGuessYou) {
        return [self searchGuessYouLoading];
    }
    if (type == IMYCKLoadingTodaySearchHotspots) {
        return [self searchHotspotsLoading];
    }
    if (type == IMYCKLoadingMemberRightsHome) {
        return [self memberRightsHomeLoading];
    }
    return nil;
}

+ (UIView *)detailLoading {
    UIView *view = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, 0)];
    CGFloat lineHeight = 20;
    CGFloat lineSpace = 20;
    /// 正文区域
    UIView *lastView = nil;
    for (int i = 0; i < 8; i++) {
        UIImageView *textLine = [[UIImageView alloc] init];
        textLine.layer.cornerRadius = 6;
        [textLine imy_setBackgroundColorForKey:kCK_Black_H];
        [view addSubview:textLine];
        if (i%4 < 2) {
            [textLine mas_makeConstraints:^(MASConstraintMaker *make) {
                make.leading.equalTo(view).offset(12);
                make.trailing.equalTo(view).offset(-12);
                if (lastView) {
                    if (i == 4) {
                        /// 这里开始画第二遍了，距离上一组高度40
                        make.top.equalTo(lastView.mas_bottom).offset(40);
                    } else {
                        make.top.equalTo(lastView.mas_bottom).offset(20);
                    }
                } else {
                    make.top.equalTo(view).offset(lineSpace);
                }
                make.height.equalTo(@12);
            }];
        } else {
            [textLine mas_makeConstraints:^(MASConstraintMaker *make) {
                make.leading.equalTo(view).offset(12);
                make.trailing.equalTo(view).offset(-120);
                make.top.equalTo(lastView.mas_bottom).offset(lineSpace);
                make.height.equalTo(@12);
            }];
        }
        lastView = textLine;
    }

    view.imy_height = 296;
    return view;
}

+ (UIView *)commentLoading {
    UIView *view = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, 0)];
    CGFloat lineHeight = 20;
    CGFloat lineSpace = 8;
    CGFloat top = 20;
    
    for (int i = 0; i < 3; i++) {
        UIView *containerView = [[UIView alloc] initWithFrame:CGRectMake(0, top, SCREEN_WIDTH, 0)];
        containerView.tag = i + 1000;
        [view addSubview:containerView];
        /// 头像
        UIImageView *subView = [[UIImageView alloc] initWithFrame:CGRectMake(12, 0, 36, 36)];
        [subView imy_setBackgroundColorForKey:kCK_Black_H];
        subView.layer.cornerRadius = 18;
        [containerView addSubview:subView];
        /// 名称
        subView = [[UIImageView alloc] initWithFrame:CGRectMake(subView.imy_right + 8, 0, 100, 15)];
        [subView imy_setBackgroundColorForKey:kCK_Black_H];
        [containerView addSubview:subView];

        subView = [[UIImageView alloc] initWithFrame:CGRectMake(subView.imy_left, subView.imy_bottom + 12, SCREEN_WIDTH - subView.imy_left - 12, lineHeight)];
        [subView imy_setBackgroundColorForKey:kCK_Black_H];
        [containerView addSubview:subView];
        
        subView = [[UIImageView alloc] initWithFrame:CGRectMake(subView.imy_left, subView.imy_bottom + lineSpace, SCREEN_WIDTH - subView.imy_left - 80, lineHeight)];
        [subView imy_setBackgroundColorForKey:kCK_Black_H];
        [containerView addSubview:subView];
        
        containerView.imy_height = subView.imy_bottom;
        
        top = containerView.imy_bottom + 24;
    }
    view.imy_height = top;
    return view;
}

+ (UIView *)topicDetailLoading {
    UIView *view = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, 0)];
    CGFloat lineHeight = 20;
    CGFloat lineSpace = 8;
    CGFloat top = 0;
    
    UIView *containerBackView = [[UIView alloc] initWithFrame:CGRectMake(12, 0, SCREEN_WIDTH - 24, 0)];
    containerBackView.layer.masksToBounds = YES;
    containerBackView.userInteractionEnabled = NO;
    containerBackView.layer.cornerRadius = 12;
    [containerBackView imy_setBackgroundColorForKey:kCK_White_AN];
    [view addSubview:containerBackView];
    
    for (int i = 0; i < 2; i++) {
        UIView *containerView = [[UIView alloc] initWithFrame:CGRectMake(0, top, SCREEN_WIDTH - 24, 0)];
        containerView.tag = i + 1000;
        [containerBackView addSubview:containerView];
        /// 头像
        UIImageView *subView = [[UIImageView alloc] initWithFrame:CGRectMake(12, 12, 48, 48)];
        [subView imy_setBackgroundColorForKey:kCK_Black_H];
        subView.layer.cornerRadius = 8;
        [containerView addSubview:subView];
        /// 名称
        subView = [[UIImageView alloc] initWithFrame:CGRectMake(subView.imy_right + 10, 22, containerView.imy_width-subView.imy_right -10-45, 12)];
        subView.layer.cornerRadius = 6;
        [subView imy_setBackgroundColorForKey:kCK_Black_H];
        [containerView addSubview:subView];

        subView = [[UIImageView alloc] initWithFrame:CGRectMake(subView.imy_left, 38, 60, 12)];
        subView.layer.cornerRadius = 6;
        [subView imy_setBackgroundColorForKey:kCK_Black_H];
        [containerView addSubview:subView];
        
        containerView.imy_height = 72;
        
        top = containerView.imy_bottom;
    }
    containerBackView.imy_height = top;
    view.imy_height = top;
    return view;
}

+ (UIView *)feedsLoading {
    UIView *view = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, 0)];
    
    UIView *container = [[UIView alloc] init];
    container.layer.cornerRadius = 8;
    container.layer.masksToBounds = YES;
    container.userInteractionEnabled = NO;
    [container imy_setBackgroundColorForKey:kCK_White_AN];

    [view addSubview:container];
    
    [container mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsMake(0, 12, 8, 12));
    }];

    UIView *avatarImageView = [[UIView alloc] initWithFrame:CGRectMake(24, 16, 36, 36)];
    [avatarImageView imy_setBackgroundColorForKey:kCK_Black_F];
    avatarImageView.layer.cornerRadius = 18;
    avatarImageView.layer.masksToBounds = YES;
    [view addSubview:avatarImageView];
    
    UIView *nameLabel = [[UIView alloc] initWithFrame:CGRectMake(avatarImageView.imy_right + 8, avatarImageView.imy_top, 70, 16)];
    [nameLabel imy_setBackgroundColorForKey:kCK_Black_F];
    [view addSubview:nameLabel];
    
    UIView *userTitleLabel = [[UIView alloc] initWithFrame:CGRectMake(avatarImageView.imy_right + 8, 38, 70, 12)];
    [userTitleLabel imy_setBackgroundColorForKey:kCK_Black_F];
    [view addSubview:userTitleLabel];
    
    UIView *followBtn = [[UIView alloc] initWithFrame:CGRectMake(SCREEN_WIDTH - 48 - 52, 22, 48, 24)];
    [followBtn imy_setBackgroundColorForKey:kCK_Black_F];
    [view addSubview:followBtn];
    
    UIView *moreBtn = [[UIView alloc] initWithFrame:CGRectMake(SCREEN_WIDTH - 16 - 24, 26, 16, 16)];
    [moreBtn imy_setBackgroundColorForKey:kCK_Black_F];
    [view addSubview:moreBtn];

    CGFloat top = 60;
    CGFloat padding = 8;
    CGFloat height = 20;
    for (int i = 0; i<4; i++) {
        UIView *lineView = [[UIView alloc] initWithFrame:CGRectMake(24, top + (height+padding)*i, SCREEN_WIDTH - 48, height)];
        [lineView imy_setBackgroundColorForKey:kCK_Black_F];
        [view addSubview:lineView];
    }

    CGFloat width = SCREEN_WIDTH - 48;
    UIView *imv = [[UIView alloc] initWithFrame:CGRectMake(24, 172, width, width*9.f/16)];
    [imv imy_setBackgroundColorForKey:kCK_Black_F];
    [view addSubview:imv];

    UIView *bottomLeft = [[UIView alloc] initWithFrame:CGRectMake(24, imv.imy_bottom + 16, 86, 20)];
    [bottomLeft imy_setBackgroundColorForKey:kCK_Black_F];
    [view addSubview:bottomLeft];

    UIView *bottomRight = [[UIView alloc] initWithFrame:CGRectMake(SCREEN_WIDTH - 24 - 103, imv.imy_bottom + 16, 103, 20)];
    [bottomRight imy_setBackgroundColorForKey:kCK_Black_F];
    [view addSubview:bottomRight];
    view.imy_height = 172 + 52 + 8 + (SCREEN_WIDTH - 48)*9.f/16;
    
    return view;
}

+ (UIView *)tipsLoading {
    UIView *contentView = [[UIView alloc] initWithFrame:CGRectMake(0, 0,  SCREEN_WIDTH - 24*2, 96.f)];
    [contentView imy_setBackgroundColorForKey:@"#F9F9F9"];
    contentView.layer.cornerRadius = 4.f;
    
    UIView *leftTopView = [UIView new];
    [contentView addSubview:leftTopView];
    [leftTopView imy_setBackgroundColorForKey:kCK_Black_L];
    [leftTopView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(12.f);
        make.top.mas_equalTo(16.f);
        make.height.mas_equalTo(24.f);
        make.right.equalTo(contentView).offset(-124.f);
    }];
    
    UIView *leftBottomView = [UIView new];
    [contentView addSubview:leftBottomView];
    [leftBottomView imy_setBackgroundColorForKey:kCK_Black_L];
    [leftBottomView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(12.f);
        make.top.equalTo(leftTopView.mas_bottom).offset(8.f);
        make.height.mas_equalTo(24.f);
        make.right.equalTo(contentView).offset(-124.f);
    }];
    
    UIView *rightView = [UIView new];
    [contentView addSubview:rightView];
    rightView.layer.cornerRadius = 4.f;
    [rightView imy_setBackgroundColorForKey:kCK_Black_L];
    [rightView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(CGSizeMake(100.f, 72.f));
        make.centerY.equalTo(contentView);
        make.right.equalTo(contentView).offset(-12.f);
    }];
    
    return contentView;
}

+ (UIView *)miniVideoFeedsLoading {
    CGFloat itemWidth = (SCREEN_WIDTH - 2*12 - 8)/2;
    CGFloat itemHeight = itemWidth*4/3 + 80;
    
    UIView *view = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, itemHeight)];

    for (int i = 0; i < 2; i++) {
        UIView *itemView = [[UIView alloc] initWithFrame:CGRectMake(12 + i*(itemWidth + 8), 2, itemWidth, itemHeight)];
        [itemView imy_setBackgroundColorForKey:kCK_White_AN];
        itemView.layer.cornerRadius = 8;
        itemView.layer.masksToBounds = YES;
        [view addSubview:itemView];
        
        UIView *imageView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, itemWidth, itemHeight - 80)];
        [imageView imy_setBackgroundColorForKey:kCK_Black_G];
        [itemView addSubview:imageView];
        
        for (int j = 0; j < 2; j++) {
            UIView *titleView = [[UIView alloc] initWithFrame:CGRectMake(8, imageView.imy_bottom + 8 + j*(14 + 6), itemView.imy_width - 16, 14)];
            [titleView imy_setBackgroundColorForKey:kCK_Black_H];
            [itemView addSubview:titleView];
        }
        
        UIView *avatar = [[UIView alloc] initWithFrame:CGRectMake(8, itemHeight - 12 - 16, 16, 16)];
        avatar.layer.cornerRadius = 8;
        avatar.layer.masksToBounds = YES;
        [avatar imy_setBackgroundColorForKey:kCK_Black_H];
        [itemView addSubview:avatar];
        
        UIView *name = [[UIView alloc] initWithFrame:CGRectMake(avatar.imy_right + 4, avatar.imy_top + 1, 32, 14)];
        [name imy_setBackgroundColorForKey:kCK_Black_H];
        [itemView addSubview:name];
        
        UIView *praise = [[UIView alloc] initWithFrame:CGRectMake(itemView.imy_width - 32 - 8, avatar.imy_top, 32, 16)];
        [praise imy_setBackgroundColorForKey:kCK_Black_H];
        [itemView addSubview:praise];
    }
    return view;;
}

+ (UIView *)todayReportLoading {
    UIView *contentView = [[UIView alloc] initWithFrame:CGRectMake(0, 0,  SCREEN_WIDTH - 24*2, 120.f)];
    [contentView imy_setBackgroundColorForKey:@"#F9F9F9"];
    contentView.layer.cornerRadius = 8.f;
    
    UIView *leftTopView = [UIView new];
    [contentView addSubview:leftTopView];
    [leftTopView imy_setBackgroundColorForKey:kCK_Black_L];
    [leftTopView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(12.f);
        make.top.mas_equalTo(12.f);
        make.height.mas_equalTo(24.f);
        make.width.mas_equalTo(72.f);
    }];
    
    UIView *textView1 = [UIView new];
    [contentView addSubview:textView1];
    [textView1 imy_setBackgroundColorForKey:kCK_Black_L];
    [textView1 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(contentView).offset(12);
        make.right.equalTo(contentView).offset(-12);
        make.top.equalTo(@46);
        make.height.equalTo(@14);
    }];
    
    UIView *textView2 = [UIView new];
    [contentView addSubview:textView2];
    [textView2 imy_setBackgroundColorForKey:kCK_Black_L];
    [textView2 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(contentView).offset(12);
        make.right.equalTo(contentView).offset(-12);
        make.top.equalTo(@70);
        make.height.equalTo(@14);
    }];
    
    UIView *textView3 = [UIView new];
    [contentView addSubview:textView3];
    [textView3 imy_setBackgroundColorForKey:kCK_Black_L];
    [textView3 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(contentView).offset(12);
        make.right.equalTo(contentView).offset(-12);
        make.top.equalTo(@94);
        make.height.equalTo(@14);
    }];
    
    return contentView;
}

+ (UIView *)todayReportSuggestLoading:(CGFloat)totalWidth {
    UIView *contentView = [[UIView alloc] initWithFrame:CGRectMake(0, 0,  totalWidth, 76.f)];
    [contentView imy_setBackgroundColorForKey:kCK_White_AN];
    
    UIView *textView1 = [UIView new];
    [contentView addSubview:textView1];
    [textView1 imy_setBackgroundColorForKey:kCK_Black_F];
    [textView1 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(contentView).offset(12);
        make.right.equalTo(contentView).offset(-12);
        make.top.equalTo(@0);
        make.height.equalTo(@20);
    }];
    
    UIView *textView2 = [UIView new];
    [contentView addSubview:textView2];
    [textView2 imy_setBackgroundColorForKey:kCK_Black_F];
    [textView2 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(contentView).offset(12);
        make.right.equalTo(contentView).offset(-12);
        make.top.equalTo(textView1.mas_bottom).mas_offset(8);
        make.height.equalTo(@20);
    }];
    
    UIView *textView3 = [UIView new];
    [contentView addSubview:textView3];
    [textView3 imy_setBackgroundColorForKey:kCK_Black_L];
    [textView3 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(contentView).offset(12);
        make.width.mas_equalTo(152);
        make.top.equalTo(textView2.mas_bottom).mas_offset(8);
        make.height.equalTo(@20);
    }];
    
    return contentView;
}

+ (UIView *)todayReportTestLoading {
    
    UIView *contentView = [[UIView alloc] initWithFrame:CGRectMake(0, 0,  (SCREEN_WIDTH - 12 * 2), 160.f)];
    [contentView imy_setBackgroundColorForKey:kCK_White_AN];
    
    UIView *textView1 = [UIView new];
    textView1.layer.cornerRadius = 6;
    [contentView addSubview:textView1];
    [textView1 imy_setBackgroundColorForKey:kCK_Black_F];
    [textView1 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(contentView).offset(20);
        make.right.equalTo(contentView).offset(-20);
        make.top.equalTo(@0);
        make.height.equalTo(@12);
    }];
    
    UIView *textView2 = [UIView new];
    textView2.layer.cornerRadius = 6;
    [contentView addSubview:textView2];
    [textView2 imy_setBackgroundColorForKey:kCK_Black_L];
    [textView2 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(contentView).offset(20);
        make.width.mas_equalTo(textView1.mas_width).multipliedBy(0.66);
        make.top.equalTo(textView1.mas_bottom).mas_offset(12);
        make.height.equalTo(@12);
    }];
    
    UIView *capsuleView1 = [UIView new];
    capsuleView1.layer.cornerRadius = 24;
    [contentView addSubview:capsuleView1];
    [capsuleView1 imy_setBackgroundColorForKey:kCK_Black_L];
    [capsuleView1 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(contentView).offset(12);
        make.right.equalTo(contentView).offset(-12);
        make.top.equalTo(textView2.mas_bottom).mas_offset(16);
        make.height.equalTo(@48);
    }];
    
    UIView *capsuleView2 = [UIView new];
    capsuleView2.layer.cornerRadius = 24;
    [contentView addSubview:capsuleView2];
    [capsuleView2 imy_setBackgroundColorForKey:kCK_Black_L];
    [capsuleView2 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(contentView).offset(12);
        make.right.equalTo(contentView).offset(-12);
        make.top.equalTo(capsuleView1.mas_bottom).mas_offset(12);
        make.height.equalTo(@48);
    }];
    
    return contentView;
}

+ (UIView *)searchGuessYouLoading {
    UIView *contentView = [[UIView alloc] initWithFrame:CGRectMake(0, 0,  SCREEN_WIDTH - 12 - 12, 64)];
    [contentView imy_setBackgroundColorForKey:kIMY_BG];
    
    UIView *line1 = [UIView new];
    line1.layer.cornerRadius = 6;
    [contentView addSubview:line1];
    [line1 imy_setBackgroundColorForKey:kCK_White_AN];
    [line1 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@12);
        make.width.equalTo(@80);
        make.top.equalTo(@4);
        make.height.equalTo(@12);
    }];
    
    UIView *line2 = [UIView new];
    line2.layer.cornerRadius = 6;
    [contentView addSubview:line2];
    [line2 imy_setBackgroundColorForKey:kCK_White_AN];
    [line2 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(line1.mas_right).offset(8);
        make.right.equalTo(contentView.mas_right).offset(-41);
        make.top.equalTo(@4);
        make.height.equalTo(@12);
    }];
    
    UIView *line3 = [UIView new];
    line3.layer.cornerRadius = 6;
    [contentView addSubview:line3];
    [line3 imy_setBackgroundColorForKey:kCK_White_AN];
    [line3 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@12);
        make.width.equalTo(@234);
        make.top.equalTo(line1.mas_bottom).offset(12);
        make.height.equalTo(@12);
    }];
    
    UIView *line4 = [UIView new];
    line4.layer.cornerRadius = 6;
    [contentView addSubview:line4];
    [line4 imy_setBackgroundColorForKey:kCK_White_AN];
    [line4 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@12);
        make.right.equalTo(contentView.mas_right).offset(-12);
        make.top.equalTo(line3.mas_bottom).offset(12);
        make.height.equalTo(@12);
    }];
    return contentView;
}

+ (UIView *)searchHotspotsLoading {
    UIView *contentView = [[UIView alloc] initWithFrame:CGRectMake(0, 0,  SCREEN_WIDTH - 12 - 12, 384)];
    [contentView imy_setBackgroundColorForKey:kCK_White_AN];
    contentView.layer.cornerRadius = 12;
    
    NSInteger y = 24;
    for (NSInteger i = 0; i < 10 ; i ++) {
        UIView *dot = [UIView new];
        dot.imy_left = 20;
        dot.imy_size = CGSizeMake(12, 12);
        dot.layer.cornerRadius = 6;
        dot.imy_top = y;
        [dot imy_setBackgroundColorForKey:kCK_Black_F];
        [contentView addSubview:dot];
        
        UIView *line = [UIView new];
        line.imy_left = dot.imy_right + 10;
        line.imy_size = CGSizeMake(SCREEN_WIDTH * 0.6, 12);
        line.layer.cornerRadius = 6;
        line.imy_top = y;
        [line imy_setBackgroundColorForKey:kCK_Black_F];
        [contentView addSubview:line];
        
        y = y + 12 + 24;
    }
    return contentView;
}

+ (UIView *)memberRightsHomeLoading {
    
    UIView *contentView = [[UIView alloc] initWithFrame:CGRectMake(0, 0,  SCREEN_WIDTH, 360)];
    [contentView imy_drawAllCornerRadius:12];
    
    {
        UIView *bg = [[UIView alloc] initWithFrame:CGRectMake(12, 0, contentView.imy_width - 24, 216)];
        [bg imy_setBackgroundColorForKey:kCK_White_AN];
        [bg imy_drawAllCornerRadius:12];
        [contentView addSubview:bg];
        
        UIView *title = [[UIView alloc] initWithFrame:CGRectMake(12, 16, 80, 12)];
        [title imy_setBackgroundColorForKey:kCK_Black_F];
        [title imy_drawAllCornerRadius:6];
        [bg addSubview:title];
        
        UIView *detail = [[UIView alloc] initWithFrame:CGRectMake(12, title.imy_bottom + 16, bg.imy_width - 24, 160)];
        [detail imy_setBackgroundColorForKey:kCK_Black_F];
        [detail imy_drawAllCornerRadius:8];
        [bg addSubview:detail];
    }
    
    {
        UIView *bg = [[UIView alloc] initWithFrame:CGRectMake(12, 0, contentView.imy_width - 24, 136)];
        [bg imy_setBackgroundColorForKey:kCK_White_AN];
        [bg imy_drawAllCornerRadius:12];
        bg.imy_top = contentView.subviews.firstObject.imy_bottom + 8;
        [contentView addSubview:bg];
        
        UIView *title = [[UIView alloc] initWithFrame:CGRectMake(12, 16, 80, 12)];
        [title imy_setBackgroundColorForKey:kCK_Black_F];
        [title imy_drawAllCornerRadius:6];
        [bg addSubview:title];
        
        UIView *detail = [[UIView alloc] initWithFrame:CGRectMake(12, title.imy_bottom + 16, bg.imy_width - 24, 80)];
        [detail imy_setBackgroundColorForKey:kCK_Black_F];
        [detail imy_drawAllCornerRadius:8];
        [bg addSubview:detail];
    }
    
    return contentView;
}


@end
