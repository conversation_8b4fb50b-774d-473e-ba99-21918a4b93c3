//
//  IMYPaddingLabel.m
//  IMYViewKit
//
//  Created by ljh on 15/5/19.
//  Copyright (c) 2015年 IMY. All rights reserved.
//

#import "IMYPaddingLabel.h"
#import "IMYViewKit.h"

@interface IMYPaddingLabel ()
@property (strong, nonatomic) UIImageView *roundedRectImageView;
@end

@implementation IMYPaddingLabel
- (instancetype)init {
    return [self initWithFrame:CGRectZero];
}
- (instancetype)initWithCoder:(NSCoder *)coder {
    self = [super initWithCoder:coder];
    if (self) {
        [self _initMyself];
    }
    return self;
}
- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self _initMyself];
    }
    return self;
}
- (void)_initMyself {
    _horizontalPadding = 2;
    _cornerRadius = 2;
    self.textAlignment = NSTextAlignmentCenter;
}

- (void)setTextAlignment:(NSTextAlignment)textAlignment {
    [super setTextAlignment:NSTextAlignmentCenter];
    //    NSLog(@"Error: IMYPaddingLabel must Center");
}
- (CGSize)intrinsicContentSize {
    CGSize parentSize = [super intrinsicContentSize];
    if (self.text.length > 0) {
        parentSize.width += 2 * _horizontalPadding;
        parentSize.height += 2 * _vericalPadding;
    }
    return parentSize;
}

- (CGSize)sizeThatFits:(CGSize)size {
    CGSize parentSize = [super sizeThatFits:size];
    if (self.text.length > 0) {
        parentSize.width += 2 * _horizontalPadding;
        parentSize.height += 2 * _vericalPadding;
    }
    return parentSize;
}
- (void)didMoveToSuperview {
    [super didMoveToSuperview];
    if (self.superview) {
        if (_roundedRectImageView) {
            _roundedRectImageView.frame = self.frame;
            [self.superview insertSubview:_roundedRectImageView belowSubview:self];
            [self.roundedRectImageView mas_updateConstraints:^(MASConstraintMaker *make) {
                make.edges.equalTo(self);
            }];
        }
    } else {
        [_roundedRectImageView removeFromSuperview];
    }
}
- (void)setRoundedRectColor:(UIColor *)roundedRectColor {
    _roundedRectColor = roundedRectColor;
    self.backgroundColor = [UIColor clearColor];

    [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(refreshRoundedRectImage) object:nil];
    [self performSelectorOnMainThread:@selector(refreshRoundedRectImage) withObject:nil waitUntilDone:NO];
}
- (void)setCornerRadius:(CGFloat)cornerRadius {
    _cornerRadius = cornerRadius;

    [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(refreshRoundedRectImage) object:nil];
    [self performSelectorOnMainThread:@selector(refreshRoundedRectImage) withObject:nil waitUntilDone:NO];
}
- (void)setFrame:(CGRect)frame {
    [super setFrame:frame];
    _roundedRectImageView.frame = frame;
}
- (void)initRoundedRectImageView {
    if (_roundedRectImageView == nil) {
        _roundedRectImageView = [[UIImageView alloc] initWithFrame:self.frame];
        if (self.superview) {
            [self.superview insertSubview:_roundedRectImageView belowSubview:self];
            [self.roundedRectImageView mas_updateConstraints:^(MASConstraintMaker *make) {
                make.edges.equalTo(self);
            }];
        }
    }
}
- (void)setText:(NSString *)text {
    [super setText:text];
    if (text.length > 0 && self.hidden == NO) {
        self.roundedRectImageView.hidden = NO;
    } else {
        self.roundedRectImageView.hidden = YES;
    }
}
- (void)setHidden:(BOOL)hidden {
    [super setHidden:hidden];
    if (hidden == NO && self.text.length > 0) {
        self.roundedRectImageView.hidden = NO;
    } else {
        self.roundedRectImageView.hidden = YES;
    }
}

- (void)refreshRoundedRectImage {
    [self initRoundedRectImageView];

    CGFloat r, g, b, a;
    [_roundedRectColor getRed:&r green:&g blue:&b alpha:&a];
    NSString *key = [NSString stringWithFormat:@"IPL_%f,%f,%f,%f,%f", r, g, b, a, _cornerRadius];
    id<IMYMemoryCacheProtocol> memoryCache = [[IMYCacheHelper sharedCacheManager] memoryCache];
    UIImage *image = [memoryCache objectForKey:key];
    if (image == nil && _cornerRadius > 0) {
        CGSize size = CGSizeMake(_cornerRadius * 2, _cornerRadius * 2);
        UIGraphicsBeginImageContextWithOptions(size, NO, SCREEN_SCALE);

        UIBezierPath *path = [UIBezierPath bezierPathWithRoundedRect:CGRectMake(0, 0, size.width, size.height) cornerRadius:_cornerRadius];
        [_roundedRectColor setFill];
        [path fill];
        image = UIGraphicsGetImageFromCurrentImageContext();
        image = [image imy_resizableImageCenter];
        UIGraphicsEndImageContext();
        [memoryCache setObject:image forKey:key];
    }
    _roundedRectImageView.image = image;
    _roundedRectImageView.hidden = !(self.text.length > 0);
}
@end
