//
//  IMYQiniuOSSFileObject.m
//  Pods
//
//  Created by ljh on 16/9/18.
//
//

#import "IMYQiniuOSSFileObject.h"

@implementation IMYQiniuOSSFileObject

- (instancetype)init {
    self = [super init];
    if (self) {
        self.uploadType = IMYOSSUploadTypeImage;
    }
    return self;
}

- (void)setUploadType:(IMYOSSUploadType)uploadType {
    _uploadType = uploadType;
    switch (uploadType) {
        case IMYOSSUploadTypeImage:
            self.contentType = @"image/jpg";
            break;
        case IMYOSSUploadTypeVideo:
            self.contentType = @"video/mpeg4";
            break;
        case IMYOSSUploadTypeFile:
            self.contentType = @"binary/octet-stream";
            break;
        case IMYOSSUploadTypeVoice:
            self.contentType = @"audio/mp3";
            break;
        case IMYOSSUploadTypeYunqiVideo:
            self.contentType = @"video/mpeg4";
            break;
        case IMYOSSUploadTypeYunqiAdvancedVideo:
            self.contentType = @"video/mpeg4";
            break;
        case IMYOSSUploadTypeYunqiOperationImage:
            self.contentType = @"image/jpg";
            break;
        case IMYOSSUploadTypeYunqiUserImage:
            self.contentType = @"image/jpg";
            break;
        case IMYOSSUploadTypeCHImage:
            self.contentType = @"image/jpg";
            break;
        case IMYOSSUploadTypeCHVideo:
            self.contentType = @"video/mpeg4";
            break;
        case IMYOSSUploadTypePAVideo:
            self.contentType = @"video/mpeg4";
        break;
    }
}

- (BOOL)isValid {
    if (self.image || self.data.length > 0) {
        return YES;
    }

    NSString *filePath = nil;
    if (self.assetUrl) {
        if ([_assetUrl.absoluteString hasPrefix:@"assets-library://"]) {
//            Class<IMYAssetsLibraryInterface> assetsLibrary = NSClassFromString(@"IMYAssetsLibrary");
//            if (assetsLibrary != nil) {
//                return YES;
//            } else {
//                NSAssert(NO, @"需要导入 IMYAssetsLibrary");
//                return NO;
//            }
            return YES;
        } else if ([_assetUrl isFileURL]) {
            filePath = [_assetUrl absoluteString];
        }
    } else if (self.path.length > 0) {
        filePath = self.path;
    }

    if (filePath.length > 0) {
        BOOL isDir = NO;
        BOOL hasExists = [[NSFileManager defaultManager] fileExistsAtPath:self.path isDirectory:&isDir];
        if (hasExists && !isDir) {
            return YES;
        }
    }

    return NO;
}

- (BOOL)isEqual:(IMYQiniuOSSFileObject *)object {
    if (self == object) {
        return YES;
    }
    if ([self.image isEqual:object.image]) {
        return YES;
    }
    if ([self.data isEqual:object.data]) {
        return YES;
    }
    if ([self.path isEqual:object.path]) {
        return YES;
    }
    if ([self.assetUrl isEqual:object.assetUrl]) {
        return YES;
    }

    return NO;
}

@end
