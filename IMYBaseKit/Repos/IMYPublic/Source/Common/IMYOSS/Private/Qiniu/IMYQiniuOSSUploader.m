//
//  IMYQiniuOSSUploader.m
//  Pods
//
//  Created by ljh on 16/9/18.
//
//

#import "IMYQiniuOSSUploader.h"
#import "IMYPublic.h"
#import "IMYQiniuOSSConfig.h"
#import "IMYQiniuOSSFileObject.h"

#import <Photos/Photos.h>

#define kQiniuHost up_qiniu_com

@interface IMYQiniuOSSBucket : NSObject

@property (nonatomic, strong) NSArray<id<IMYOSSFileObject>> *allObjects;
@property (nonatomic, copy) IMYOSSUploaderProgressBlock progressBlock;
@property (nonatomic, copy) IMYOSSUploaderCompletedBlock completedBlock;
@property (nonatomic, copy) IMYOSSUploaderAllCompletedBlock allCompletedBlock;

@end

@implementation IMYQiniuOSSBucket

@end


#pragma mark -

@interface IMYQiniuOSSUploader ()

@property (nonatomic, strong) dispatch_queue_t queue;
@property (nonatomic, strong) NSMutableArray<IMYQiniuOSSBucket *> *allBuckets;

@end

@implementation IMYQiniuOSSUploader
- (instancetype)init {
    self = [super init];
    if (self) {
        _config = [IMYQiniuOSSConfig new];
        _queue = dispatch_queue_create("IMYQiniuOSSUploader.Queue", NULL);
        _allBuckets = [NSMutableArray array];
    }
    return self;
}

- (void)uploadObject:(id<IMYOSSFileObject>)anObject
       progressBlock:(IMYOSSUploaderProgressBlock)progressBlock
      complatedBlock:(IMYOSSUploaderCompletedBlock)completedBlock {
    if (![anObject isKindOfClass:[IMYQiniuOSSFileObject class]]) {
        NSAssert(NO, @"anObject 无效");
        if (completedBlock) {
            dispatch_async(dispatch_get_main_queue(), ^{
                completedBlock(anObject, [NSError errorWithDomain:@"anObject 无效"
                                                             code:NSURLErrorBadURL
                                                         userInfo:nil]);
            });
        }
        return;
    }

    [self uploadAllObjects:@[anObject] progressBlock:progressBlock complatedBlock:completedBlock allComplatedBlock:nil];
}

- (void)uploadAllObjects:(NSArray<id<IMYOSSFileObject>> *)allObjects
           progressBlock:(IMYOSSUploaderProgressBlock)progressBlock
          complatedBlock:(IMYOSSUploaderCompletedBlock)completedBlock
       allComplatedBlock:(IMYOSSUploaderAllCompletedBlock)allCompletedBlock {
    __block BOOL isInvalid = NO;
    [allObjects enumerateObjectsUsingBlock:^(IMYQiniuOSSFileObject *obj, NSUInteger idx, BOOL *_Nonnull stop) {
        if (![obj isKindOfClass:[IMYQiniuOSSFileObject class]]) {
            isInvalid = YES;
            *stop = YES;
            return;
        }
    }];
    if (isInvalid) {
        NSAssert(NO, @"allObjects 无效");
        if (allCompletedBlock) {
            dispatch_async(dispatch_get_main_queue(), ^{
                allCompletedBlock(allObjects);
            });
        }
        return;
    }

    dispatch_async(self.queue, ^{
        NSMutableArray *fileNames = [NSMutableArray array];
        __block BOOL hasExistsFile = NO;
        [allObjects enumerateObjectsUsingBlock:^(IMYQiniuOSSFileObject *obj, NSUInteger idx, BOOL *_Nonnull stop) {
            if ([obj isValid]) {
                hasExistsFile = YES;
            }
            [fileNames addObject:obj.name ?: @""];
        }];

        if (hasExistsFile) {
            [self.config requestTokenWithNames:fileNames
                                     complated:^(NSArray<NSString *> *_Nonnull tokens) {
                                         [tokens enumerateObjectsUsingBlock:^(NSString *_Nonnull token, NSUInteger idx, BOOL *_Nonnull stop) {
                                             IMYQiniuOSSFileObject *fileObj = allObjects[idx];
                                             fileObj.state = IMYOSSFileStateInQueue;
                                             fileObj.token = token;
                                         }];
                                         dispatch_async(self.queue, ^{
                                             [self _uploadAllObjects:allObjects progressBlock:progressBlock complatedBlock:completedBlock allComplatedBlock:allCompletedBlock];
                                         });
                                     }];
        } else {
            if (completedBlock) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    [allObjects enumerateObjectsUsingBlock:^(id<IMYOSSFileObject> _Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
                        completedBlock(obj, obj.error);
                    }];
                });
            }
            if (allCompletedBlock) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    allCompletedBlock(allObjects);
                });
            }
        }
    });
}

- (void)_uploadAllObjects:(NSArray<id<IMYOSSFileObject>> *)allObjects
            progressBlock:(IMYOSSUploaderProgressBlock)progressBlock
           complatedBlock:(IMYOSSUploaderCompletedBlock)completedBlock
        allComplatedBlock:(IMYOSSUploaderAllCompletedBlock)allCompletedBlock {
    IMYQiniuOSSBucket *bucket = [IMYQiniuOSSBucket new];
    bucket.allObjects = allObjects;
    bucket.progressBlock = progressBlock;
    bucket.completedBlock = completedBlock;
    bucket.allCompletedBlock = allCompletedBlock;

    [self.allBuckets addObject:bucket];

    [self startUploadAllObjects:allObjects bucket:bucket];
}

- (void)startUploadObject:(IMYQiniuOSSFileObject *)anObject
            progressBlock:(IMYOSSUploaderProgressBlock)progressBlock
           complatedBlock:(IMYOSSUploaderCompletedBlock)completedBlock {
    NSMutableDictionary *parameters = [[NSMutableDictionary alloc] init];
    [parameters setValue:anObject.token forKey:@"token"];
    [parameters setValue:anObject.name forKey:@"key"];

    anObject.state = IMYOSSFileStateUploading;

    IMYHTTPBuildable *buildable = [IMYPublicServerRequest buildable].Host(kQiniuHost).Post().Parameters(parameters).FormData(^(id<AFMultipartFormData> formData) {
        NSData *data = anObject.data;
        if (anObject.image) {
            data = [anObject.image imy_toData];
        }
        if (data) {
            [formData appendPartWithFileData:data name:@"file" fileName:anObject.name mimeType:@"image/jpeg"];
        }

        if (anObject.path) {
            NSURL *fileUrl = [NSURL fileURLWithPath:anObject.path];
            [formData appendPartWithFileURL:fileUrl name:@"file" fileName:anObject.name mimeType:@"image/jpeg" error:nil];
        }

        if (anObject.assetUrl) {
            if ([anObject.assetUrl.absoluteString hasPrefix:@"assets-library://"]) {
                [self assetInputStreamWithURL:anObject.assetUrl formData:formData fileObject:anObject];
            } else {
                [formData appendPartWithFileURL:anObject.assetUrl name:@"file" fileName:anObject.name mimeType:@"image/jpeg" error:nil];
            }
        }
    });

    buildable.Progress(^(int64_t totalBytesRead, int64_t totalBytesExpectedToRead) {
        double progress = 0;
        if (totalBytesExpectedToRead > 0) {
            progress = totalBytesRead / (double)totalBytesExpectedToRead;
        }
        if (progressBlock) {
            progressBlock(anObject, progress);
        }
    });

    [buildable.signal subscribeNext:^(IMYHTTPResponse *x) {
        NSDictionary *dict = [x responseObject];
        NSString *key = [dict valueForKey:@"key"];
        anObject.url = [self remoteURLWithName:key];
        anObject.state = IMYOSSFileStateCompleted;
        if (completedBlock) {
            dispatch_async(dispatch_get_main_queue(), ^{
                completedBlock(anObject, nil);
            });
        }
    }
        error:^(NSError *error) {
            if (error.af_httpResponse.statusCode == 614) {
                anObject.state = IMYOSSFileStateCompleted;
                anObject.url = [self remoteURLWithName:anObject.name];
            } else {
                anObject.state = IMYOSSFileStateError;
                anObject.error = error;
            }
            if (completedBlock) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    completedBlock(anObject, anObject.error);
                });
            }
        }];
}

- (void)assetInputStreamWithURL:(NSURL *)url formData:(id<AFMultipartFormData>)formData fileObject:(IMYQiniuOSSFileObject *)fileObject {
    NSAssert(NO == [NSThread isMainThread], @"This method can not run on main thread!");
//    Class<IMYAssetsLibraryInterface> assetsLibrary = NSClassFromString(@"IMYAssetsLibrary");
//    NSAssert(assetsLibrary != nil, @"需要导入 IMYAssetsLibrary");
//
//    dispatch_semaphore_t sema = dispatch_semaphore_create(0);
//    [assetsLibrary assetForURL:url
//                    completion:^(id<IMYAssetInterface> asset) {
//                        if (asset) {
//                            NSInputStream *stream = [NSInputStream pos_inputStreamForAFNetworkingWithAssetURL:url];
//                            [stream setProperty:@0 forKey:NSStreamFileCurrentOffsetKey];
//                            [stream open];
//                            long long size = [stream pos_streamLength];
//                            NSLog(@"open stream: %lld", size);
//                            if (fileObject.name && size > 0 && asset.MIMEType) {
//                                [formData appendPartWithInputStream:stream name:@"file" fileName:fileObject.name length:size mimeType:asset.MIMEType];
//                            }
//                        }
//                        dispatch_semaphore_signal(sema);
//                    }];
//    dispatch_semaphore_wait(sema, DISPATCH_TIME_FOREVER);
    
    PHFetchOptions *options = [PHFetchOptions new];
    options.wantsIncrementalChangeDetails = NO;
    PHFetchResult *result = [PHAsset fetchAssetsWithALAssetURLs:@[url] options:options];
    if (result.count == 0) {
        NSError *error = [NSError errorWithDomain:@"com.meetyou.basekit.oss.filepath.error"
                                             code:-1
                                         userInfo:@{NSLocalizedDescriptionKey: @"Image not found"}];
        NSLog(@"%s: %@", __func__, error.localizedDescription);
        return;
    }
    PHAsset *phAsset = result.firstObject;
    PHImageRequestOptions *requestOptions = [[PHImageRequestOptions alloc] init];
    requestOptions.deliveryMode = PHImageRequestOptionsDeliveryModeHighQualityFormat;
    requestOptions.resizeMode = PHImageRequestOptionsResizeModeNone;
    requestOptions.networkAccessAllowed = YES;// 默认NO,如果图片在icloud上，会去icloud下载
    requestOptions.progressHandler = nil;
    requestOptions.synchronous = YES;
    [[PHImageManager defaultManager] requestImageDataForAsset:phAsset
                                                      options:options
                                                resultHandler:^(NSData * _Nullable imageData, NSString * _Nullable dataUTI, UIImageOrientation orientation, NSDictionary * _Nullable info) {
        if (imageData) {
            NSString *mime = (__bridge_transfer NSString *)UTTypeCopyPreferredTagWithClass((__bridge CFStringRef)dataUTI,
                                                                                           kUTTagClassMIMEType);
            [formData appendPartWithFileData:imageData
                                        name:@"file"
                                    fileName:fileObject.name
                                    mimeType:mime];
        } else {
            NSError *error = [NSError errorWithDomain:@"com.meetyou.basekit.oss.filepath.error"
                                                 code:-1
                                             userInfo:@{NSLocalizedDescriptionKey: @"Image not found"}];
            NSLog(@"%s: %@", __func__, error.localizedDescription);
        }
    }];
    
}

- (void)startUploadAllObjects:(NSArray<IMYQiniuOSSFileObject *> *)allObjects bucket:(IMYQiniuOSSBucket *)bucket {
    __block BOOL allComplated = YES;
    [allObjects enumerateObjectsUsingBlock:^(IMYQiniuOSSFileObject *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
        if (obj.state == IMYOSSFileStateInQueue) {
            if (!obj.token.length) {
                obj.state = IMYOSSFileStateError;
                obj.error = [NSError errorWithDomain:@"授权配置无效!"
                                                code:NSURLErrorBadServerResponse
                                            userInfo:nil];
            } else {
                allComplated = NO;
                *stop = YES;

                [self startUploadObject:obj
                          progressBlock:bucket.progressBlock
                         complatedBlock:^(id<IMYOSSFileObject> _Nonnull object, NSError *_Nonnull error) {
                             if (bucket.completedBlock) {
                                 bucket.completedBlock(object, error);
                             }
                             dispatch_async(self.queue, ^{
                                 [self startUploadAllObjects:allObjects bucket:bucket];
                             });
                         }];
            }
        } else if (obj.state != IMYOSSFileStateCompleted && obj.state != IMYOSSFileStateError) {
            allComplated = NO;
        }
    }];

    if (!allComplated) {
        return;
    }

    if (bucket.allCompletedBlock) {
        dispatch_async(dispatch_get_main_queue(), ^{
            bucket.allCompletedBlock(allObjects);
        });
    }

    [self.allBuckets removeObject:bucket];
}

- (NSURL *)remoteURLWithName:(NSString *)name {
    NSString *urlString = [NSString stringWithFormat:@"%@/%@", sc_seeyouyima_com, name];
    return [NSURL URLWithString:urlString];
}

- (void)stopUpload {
    NSAssert(NO, @"NO Impl");
}
- (void)startUpload {
    NSAssert(NO, @"NO Impl");
}


- (void)getAllObjects:(void (^)(NSArray<id<IMYOSSFileObject>> *))block {
    dispatch_async(self.queue, ^{
        NSMutableArray *allObjects = [NSMutableArray array];
        [self.allBuckets enumerateObjectsUsingBlock:^(IMYQiniuOSSBucket *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
            [allObjects addObjectsFromArray:obj.allObjects];
        }];
        if (block) {
            dispatch_async(dispatch_get_main_queue(), ^{
                block(allObjects);
            });
        }
    });
}

- (void)removeObject:(IMYQiniuOSSFileObject *)anObject
      complatedBlock:(void (^)(id<IMYOSSFileObject>, BOOL))completedBlock {
    if (![anObject isKindOfClass:[IMYQiniuOSSFileObject class]]) {
        NSAssert(NO, @"anObject 无效");
        if (completedBlock) {
            dispatch_async(dispatch_get_main_queue(), ^{
                completedBlock(anObject, NO);
            });
        }
        return;
    }

    dispatch_async(self.queue, ^{
        __block BOOL isRemoved = NO;
        [self.allBuckets enumerateObjectsUsingBlock:^(IMYQiniuOSSBucket *_Nonnull bucket, NSUInteger idx, BOOL *_Nonnull stop) {
            [bucket.allObjects enumerateObjectsUsingBlock:^(IMYQiniuOSSFileObject *_Nonnull fileObject, NSUInteger idx, BOOL *_Nonnull stop) {
                if ([fileObject isEqual:anObject]) {
                    anObject.state = IMYOSSFileStateError;
                    anObject.error = [NSError errorWithDomain:@"已取消"
                                                         code:NSURLErrorCancelled
                                                     userInfo:nil];
                    isRemoved = YES;
                }
            }];
        }];
        if (completedBlock) {
            dispatch_async(dispatch_get_main_queue(), ^{
                completedBlock(anObject, isRemoved);
            });
        }
    });
}

#pragma mark - FileObject

- (IMYQiniuOSSFileObject *)fileObjectWithName:(NSString *)name {
    IMYQiniuOSSFileObject *fileObject = [IMYQiniuOSSFileObject new];
    fileObject.name = name;
    return fileObject;
}

- (id<IMYOSSFileObject>)fileObjectWithName:(NSString *)name data:(NSData *)data {
    IMYQiniuOSSFileObject *fileObject = [self fileObjectWithName:name];
    fileObject.data = data;
    return fileObject;
}

- (id<IMYOSSFileObject>)fileObjectWithName:(NSString *)name path:(NSString *)path {
    IMYQiniuOSSFileObject *fileObject = [self fileObjectWithName:name];
    fileObject.path = path;
    return fileObject;
}

- (id<IMYOSSFileObject>)fileObjectWithName:(NSString *)name image:(UIImage *)image {
    IMYQiniuOSSFileObject *fileObject = [self fileObjectWithName:name];
    fileObject.image = image;
    return fileObject;
}

- (id<IMYOSSFileObject>)fileObjectWithName:(NSString *)name assetUrl:(NSURL *)assetUrl {
    IMYQiniuOSSFileObject *fileObject = [self fileObjectWithName:name];
    fileObject.assetUrl = assetUrl;
    return fileObject;
}

@end
