//
//  IMYAliyunOSSFileObject.m
//  IMYOSSDemo
//
//  Created by ljh on 16/7/28.
//  Copyright © 2016年 ljh. All rights reserved.
//

#import "IMYAliyunOSSFileObject.h"
#import "NSObject+IMYPrintLog.h"
#import "IMYFoundation.h"
#import <Photos/Photos.h>

@interface IMYAliyunOSSFileObject ()
@property (nonatomic, copy) NSString *filePath;
@property (nonatomic, assign) long fileSize;
@end

@implementation IMYAliyunOSSFileObject

- (instancetype)init {
    self = [super init];
    if (self) {
        self.autoConvertHEIC = YES;
        self.uploadType = IMYOSSUploadTypeImage;
        self.concurrencyCount = 1;
    }
    return self;
}

- (NSString *)name {
    if (!_name.length) {
        _name = self.filePath.lastPathComponent;
    }
    return _name;
}

- (void)setUploadType:(IMYOSSUploadType)uploadType {
    _uploadType = uploadType;
    // 默认当图片处理
    self.contentType = @"image/jpg";
    switch (uploadType) {
        case IMYOSSUploadTypeImage:
            self.contentType = @"image/jpg";
            break;
        case IMYOSSUploadTypeVideo:
            self.contentType = @"video/mpeg4";
            break;
        case IMYOSSUploadTypeFile:
            self.contentType = @"binary/octet-stream";
            break;
        case IMYOSSUploadTypeVoice:
            self.contentType = @"audio/mp3";
            break;
        case IMYOSSUploadTypeYunqiVideo:
            self.contentType = @"video/mpeg4";
            break;
        case IMYOSSUploadTypeYunqiAdvancedVideo:
            self.contentType = @"video/mpeg4";
            break;
        case IMYOSSUploadTypeYunqiOperationImage:
            self.contentType = @"image/jpg";
            break;
        case IMYOSSUploadTypeYunqiUserImage:
            self.contentType = @"image/jpg";
            break;
        case IMYOSSUploadTypeCHImage:
            self.contentType = @"image/jpg";
            break;
        case IMYOSSUploadTypeCHVideo:
            self.contentType = @"video/mpeg4";
            break;
        case IMYOSSUploadTypeCommunityVideo:
            self.contentType = @"video/mpeg4";
            break;
        case IMYOSSUploadTypeSecurityImage:
            self.contentType = @"image/jpg";
            break;
        case IMYOSSUploadTypePAVideo:
            self.contentType = @"video/mpeg4";
        break;
        case IMYOSSUploadTypeIMImage:
            self.contentType = @"image/jpg";
            break;
        case IMYOSSUploadTypeFetalHeart:
            self.contentType = @"audio/wav";
            break;
    }
}

- (void)setConcurrencyCount:(NSInteger)concurrencyCount {
    if (concurrencyCount < 1) {
        concurrencyCount = 1;
    }
    if (concurrencyCount > 5) {
        concurrencyCount = 5;
    }
    _concurrencyCount = concurrencyCount;
}

- (BOOL)isValid {
    NSString *filePath = [self filePath];
    BOOL isDirectory = NO;
    BOOL isExists = [[NSFileManager defaultManager] fileExistsAtPath:filePath isDirectory:&isDirectory];
    if (!isExists || isDirectory) {
        self.error = [NSError errorWithDomain:@"文件不存在"
                                         code:IMYOSSErrorCodeFileNotFound
                                     userInfo:nil];
        self.state = IMYOSSFileStateError;
        return NO;
    }
    return YES;
}

- (NSString *)filePath {
    if (_filePath) {
        // 直接返回
        return _filePath;
    } else if (_path) {
        _filePath = _path;
    } else if (_data) {
        _filePath = [self createTempFilePath];
        [_data writeToFile:_filePath atomically:YES];
    } else if (_image) {
        _filePath = [self createTempFilePath];
        NSData *data = [_image imy_toData];
        [data writeToFile:_filePath atomically:YES];
    } else if (_assetUrl) {
        if ([_assetUrl.absoluteString hasPrefix:@"assets-library://"]) {
//            Class<IMYAssetsLibraryInterface> assetsLibrary = NSClassFromString(@"IMYAssetsLibrary");
//            NSAssert(assetsLibrary != nil, @"需要导入 IMYAssetsLibrary");
//            dispatch_semaphore_t sema = dispatch_semaphore_create(0);
//            [assetsLibrary assetForURL:_assetUrl
//                            completion:^(id<IMYAssetInterface> asset) {
//                                if (asset) {
//                                    NSInputStream *stream = [NSInputStream pos_inputStreamForAFNetworkingWithAssetURL:asset.url];
//                                    [stream open];
//                                    long long size = [stream pos_streamLength];
//                                    if (size > 0) {
//                                        NSString *tempFilePath = [self createTempFilePath];
//                                        NSFileHandle *fileHandle = [NSFileHandle fileHandleForWritingAtPath:tempFilePath];
//                                        uint8_t buffer[10240];
//                                        NSInteger readLength = 0;
//                                        do {
//                                            readLength = [stream read:(uint8_t *)(&buffer)maxLength:10240];
//                                            if (readLength > 0) {
//                                                NSData *data = [NSData dataWithBytes:&buffer length:readLength];
//                                                [fileHandle writeData:data];
//                                            }
//                                        } while (readLength > 0);
//                                        [fileHandle closeFile];
//                                        self->_filePath = tempFilePath;
//                                    }
//                                    [stream close];
//                                }
//                                dispatch_semaphore_signal(sema);
//                            }];
//            dispatch_semaphore_wait(sema, DISPATCH_TIME_FOREVER);
            
            PHFetchOptions *options = [PHFetchOptions new];
            options.wantsIncrementalChangeDetails = NO;
            NSURL *url = [NSURL URLWithString:_assetUrl];
            PHFetchResult *result = [PHAsset fetchAssetsWithALAssetURLs:@[url] options:options];
//            if (result.count == 0) {
//                NSError *error = [NSError errorWithDomain:@"com.meetyou.basekit.oss.filepath.error"
//                                                     code:-1
//                                                 userInfo:@{NSLocalizedDescriptionKey: @"Image not found"}];
//                return nil;
//            }
            PHAsset *phAsset = result.firstObject;
            PHImageRequestOptions *requestOptions = [[PHImageRequestOptions alloc] init];
            requestOptions.deliveryMode = PHImageRequestOptionsDeliveryModeHighQualityFormat;
            requestOptions.resizeMode = PHImageRequestOptionsResizeModeNone;
            requestOptions.networkAccessAllowed = YES;// 默认NO,如果图片在icloud上，会去icloud下载
            requestOptions.progressHandler = nil;
            requestOptions.synchronous = YES;
            [[PHImageManager defaultManager] requestImageDataForAsset:phAsset
                                                              options:options
                                                        resultHandler:^(NSData * _Nullable imageData, NSString * _Nullable dataUTI, UIImageOrientation orientation, NSDictionary * _Nullable info) {
                if (imageData) {
                    NSString *tempFilePath = [self createTempFilePath];
                    [imageData writeToFile:tempFilePath atomically:YES];
                    self->_filePath = tempFilePath;
                }
            }];
           
        } else if ([_assetUrl isFileURL]) {
            _filePath = [self createTempFilePath];
            [[NSFileManager defaultManager] removeItemAtPath:_filePath error:nil];
            [[NSFileManager defaultManager] copyItemAtPath:_assetUrl.path toPath:_filePath error:nil];
        } else {
            PHFetchResult<PHAsset *> *result = [PHAsset fetchAssetsWithLocalIdentifiers:@[_assetUrl.absoluteString] options:nil];
            if (result.count > 0) {
                PHImageRequestOptions *requestOptions = [[PHImageRequestOptions alloc] init];
                requestOptions.synchronous = YES;
                requestOptions.networkAccessAllowed = YES;
                [[PHCachingImageManager defaultManager] requestImageDataForAsset:result.firstObject options:requestOptions resultHandler:^(NSData * _Nullable imageData, NSString * _Nullable dataUTI, UIImageOrientation orientation, NSDictionary * _Nullable info) {
                    if (info && imageData) {
                        self->_filePath = [self createTempFilePath];
                        [imageData writeToFile:self->_filePath atomically:YES];
                    }
                }];
            }
        }
    }
    // 检测文件格式 是否需要转码
    [self checkFormatWithFilePath:_filePath];
    // 需要重新计算文件大小
    _fileSize = 0;
    return _filePath;
}

- (long)fileSize {
    if (_fileSize > 0) {
        return _fileSize;
    }
    NSDictionary *fileDict = [[NSFileManager defaultManager] attributesOfItemAtPath:self.filePath error:nil];
    _fileSize = [fileDict[NSFileSize] longValue];
    return _fileSize;
}

- (void)checkFormatWithFilePath:(NSString *)filePath {
    if (!filePath.length) {
        return;
    }
    if (self.autoConvertHEIC && [self isHEICWithFilePath:filePath]) {
        UIImage *image = [UIImage imageWithContentsOfFile:filePath];
        NSData *jpgData = UIImageJPEGRepresentation(image, 0.95);
        [jpgData writeToFile:filePath atomically:YES];
    }
}

- (BOOL)isHEICWithFilePath:(NSString *)filePath {
    NSFileHandle *fileHandle = [NSFileHandle fileHandleForReadingAtPath:filePath];
    [fileHandle seekToFileOffset:4];
    NSData *data = [fileHandle readDataOfLength:8];
    [fileHandle closeFile];
    if (data != nil) {
        //....ftypheic ....ftypheix ....ftyphevc ....ftyphevx
        NSString *testString = [[NSString alloc] initWithData:data encoding:NSASCIIStringEncoding];
        if ([testString isEqualToString:@"ftypheic"]
            || [testString isEqualToString:@"ftypheix"]
            || [testString isEqualToString:@"ftyphevc"]
            || [testString isEqualToString:@"ftyphevx"]) {
            return YES;
        }
        //....ftypmif1 ....ftypmsf1
        if ([testString isEqualToString:@"ftypmif1"] ||
            [testString isEqualToString:@"ftypmsf1"]) {
            return YES;
        }
    }
    return NO;
}

- (BOOL)isEqual:(IMYAliyunOSSFileObject *)object {
    if (self == object) {
        return YES;
    }
    if ([self.image isEqual:object.image]) {
        return YES;
    }
    if ([self.data isEqual:object.data]) {
        return YES;
    }
    if ([self.path isEqual:object.path]) {
        return YES;
    }
    if ([self.assetUrl isEqual:object.assetUrl]) {
        return YES;
    }
    if ([self.filePath isEqual:object.filePath]) {
        return YES;
    }
    return NO;
}

- (NSString *)createTempFilePath {
    NSString *fileName = [[NSUUID UUID] UUIDString];
    NSString *tempFilePath = [[NSString imy_tmpDirectory] stringByAppendingPathComponent:fileName];
    [[NSFileManager defaultManager] createFileAtPath:tempFilePath contents:nil attributes:nil];
    return tempFilePath;
}

- (void)setState:(IMYOSSFileState const)state {
    _state = state;
    // 当完成上传时，需要删除临时文件
    if (state == IMYOSSFileStateCompleted ||
        state == IMYOSSFileStateError) {
        // 判断 filePath 是否是临时路径 （不是业务传递的路径 都是临时路径）
        if (!_path && _filePath) {
            [[NSFileManager defaultManager] removeItemAtPath:_filePath error:nil];
        }
    }
}

- (NSArray<NSError *> *)lastErrors {
    if (_lastErrors && _error) {
        if ([_lastErrors containsObject:_error]) {
            // 移除当前持有的错误信息
            NSMutableArray *mutableArray = [_lastErrors mutableCopy];
            [mutableArray removeObject:_error];
            _lastErrors = [mutableArray copy];
        }
    }
    return _lastErrors;
}

- (void)appendLastError:(NSError *)error {
    if (!error) {
        return;
    }
    // 如果有内联错误，则使用内联错误
    NSError *underlyingError = error.userInfo[NSUnderlyingErrorKey] ?: error;
    if (!_lastErrors) {
        _lastErrors = @[underlyingError];
    } else {
        _lastErrors = [_lastErrors arrayByAddingObject:underlyingError];
    }
}

- (NSString *)description {
    return [self imy_getDefaultDescription];
}

@end
