//
//  IMYQYSessionManager.m
//  IMYPublic
//
//  Created by ljh on 2021/1/27.
//

#import "IMYQYSessionManager.h"
#import "IMYPublic.h"

// 没有强制依赖，无法直接使用对应类
@protocol IMYUpinQYSDK <NSObject>
+ (instancetype)sharedSDK;
- (void)registerAppId:(NSString *)appKey appName:(NSString *)appName;
- (void)setFollowSystemLanguage:(BOOL)follow;
- (UIViewController *)sessionViewController;
- (id)conversationManager;
- (id)customUIConfig;
- (id)customActionConfig;
- (void)updateApnsToken:(NSData *)token;
- (BOOL)isForbiddenInput;

// QYConversationManager Class
- (NSInteger)allUnreadCount;
- (void)clearUnreadCount;
- (void)clearUnreadCount:(NSString *)shopId;
- (NSArray *)getSessionList;

// QYCustomUIConfig Class
@property (nonatomic, assign) NSInteger bypassDisplayMode;

// QYCustomActionConfig Class
@property (nonatomic, assign) BOOL pullRoamMessage;

// 设置绑定的uid
- (void)setUserInfo:(id)userInfo userInfoResultBlock:(void(^)(BOOL success, NSError *error))userInfoBlock;
- (void)logout:(void(^)(BOOL))completion;
- (NSString *)currentUserID;


// QYUserInfo Class
@property (nonatomic, copy) NSString *userId;
@property (nonatomic, copy) NSString *data;

@end

#pragma mark - Private Headers

@interface IMYQYSessionManager ()
// 收到新数据
@property (nonatomic, strong) RACSignal *updatedSignal;
// 未读消息数
@property (nonatomic, assign) NSInteger allUnreadCount;
// 所有会话
@property (nonatomic, copy) NSArray<IMYQYSessionInfo *> *sessions;
// QYSDK 是否注册完毕
@property (atomic, assign) BOOL isRegisted;
// APNS Token
@property (atomic, copy) NSData *deviceToken;
@end


@interface IMYQYSessionInfo ()
/// ID
@property (nonatomic, copy) NSString *sessionId;
/// 名称
@property (nonatomic, copy) NSString *sessionName;
/// 头像URL
@property (nonatomic, copy) NSString *sessionIcon;
/// 会话未读数
@property (nonatomic, assign) NSInteger unreadCount;
/// 会话最后一条消息文本
@property (nonatomic, copy) NSString *lastMessageText;
/// 会话最后一条消息的时间
@property (nonatomic, assign) NSTimeInterval lastMessageTimeStamp;
/// 跳转URI
@property (nonatomic, copy) NSString *actionURI;
/// 是否是收到的消息
@property (nonatomic, assign) BOOL isReceivedMsg;

@end

#pragma mark - Impl

@implementation IMYQYSessionManager

IMY_KYLIN_FUNC_MAINTAB_ASYNC {
    // 默认不在启动阶段初始化，除非有远程配置
    // 之前发现初始化可能有5-6秒以上,独立一个线程进行处理
    imy_asyncBlock(^{
        id lazyinit = [[IMYConfigsCenter sharedInstance] stringForKeyPath:@"apptech.qiyu_sdk.lazy"];
        if (lazyinit && ![lazyinit boolValue]) {
            [[IMYQYSessionManager sharedInstance] setupQYSDK];
        }
    });
}

+ (instancetype)sharedInstance {
    static id instance;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [self new];
    });
    return instance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        self.updatedSignal = [RACReplaySubject replaySubjectWithCapacity:1];
    }
    return self;
}

- (id<IMYUpinQYSDK>)getQYSDK {
    id<IMYUpinQYSDK> QYSDK = [NSClassFromString(@"QYSDK") sharedSDK];
    return QYSDK;
}

- (UIViewController *)getQYSessionViewController {
    id<IMYUpinQYSDK> QYSDK = [self getQYSDK];
    UIViewController *sessionViewController = [QYSDK sessionViewController];
    return sessionViewController;
}

+ (NSString *)getQYSessionViewControllerClassName {
    return @"QYSessionViewController";
}

- (void)setupQYSDK {
    if (![IMYPublicAppHelper shareAppHelper].hasAgreedPrivacy) {
        // 如果用户 未同意隐私政策声明，则暂时不注册SDK，每隔 1秒进行轮训
        imy_asyncMainBlockInDefaultMode(1, ^{
            [self setupQYSDK];
        });
        return;
    }
    
    // 已注册
    if (self.isRegisted) {
        return;
    }
    
    if (IMYGetApplicationState() == UIApplicationStateBackground) {
        // 处于后台状态，等待下次激活再注册
        imy_asyncMainBlockInDefaultMode(1, ^{
            [self setupQYSDK];
        });
        return;
    }
    
    // 远程控制是否在异步线程进行初始化
    const BOOL mainqueue = [[IMYConfigsCenter sharedInstance] boolForKeyPath:@"apptech.qiyu_sdk.main"];
    if (mainqueue) {
        // 在主线程注册七鱼SDK
        imy_asyncMainExecuteBlock(^{
            @synchronized (self) {
                [self registerQYSDK];
            }
            imy_throttle_on_queue(0.2, @"IMYQYSessionUpdate", dispatch_get_main_queue(), ^{
                [self updateSessions];
            });
        });
    } else {
        // 在异步线程，注册七鱼SDK
        imy_asyncBlockExecuteBlock(^{
            @synchronized (self) {
                [self registerQYSDK];
            }
            imy_throttle_on_queue(0.2, @"IMYQYSessionUpdate", dispatch_get_main_queue(), ^{
                [self updateSessions];
            });
        });
    }
}

- (void)registerQYSDK {
    if (self.isRegisted) {
        return;
    }
    // 注册七鱼SDK
    id<IMYUpinQYSDK> QYSDK = [self getQYSDK];
    // 只支持简体中文，不切换语言
    if ([QYSDK respondsToSelector:@selector(setFollowSystemLanguage:)]) {
        [QYSDK setFollowSystemLanguage:NO];
    }
    // 初始化QYSDK
    [QYSDK registerAppId:@"5e072707a57ff4d5d7a57654c04532a7"
                 appName:[IMYPublicAppHelper shareAppHelper].appName];
    
    // 标记已注册，主要是设置 old uid 判断用
    self.isRegisted = YES;
    
    // 监听未读消息数量的变化
    [[QYSDK conversationManager] setDelegate:(id)self];
    
    // 隐藏访客分流弹窗
    [[QYSDK customUIConfig] setBypassDisplayMode:0];
    
    // 默认拉取漫游消息
    if (IMYQYSessionManager.enableQYPullRoamMessage) {
        [[QYSDK customActionConfig] setPullRoamMessage:YES];
    }
    
    // 绑定uid
    [[IMYPublicAppHelper shareAppHelper].useridChangedSignal subscribeNext:^(id x) {
        [self updateQYSDKUserInfo];
    }];
    
    // APNS Token
    imy_asyncMainBlockInDefaultMode(1, ^{
        if (self.deviceToken) {
            [self updateApnsToken:self.deviceToken];
        }
    });
}

+ (BOOL)enableQYPullRoamMessage {
    return YES;
}

- (void)setOldUid:(NSString *)oldUid {
    if ([_oldUid isEqualToString:oldUid]) {
        return;
    }
    _oldUid = [oldUid copy];
    // SDK已注册完毕，更新对应 userinfo
    if (self.isRegisted) {
        [self updateQYSDKUserInfo];
    }
}

- (void)updateQYSDKUserInfo {
    // 获取用户信息传给七鱼
    imy_throttle_on_queue(0.2, @"IMYQYSDK-UpdateUserInfo", dispatch_get_main_queue(), ^{
        static BOOL kQYUserInfoSetting = NO;
        if (kQYUserInfoSetting) {
            // 正在设置中，1秒后重试
            imy_throttle(1, ^{
                [self updateQYSDKUserInfo];
            });
            return;
        }
        kQYUserInfoSetting = YES;
        [self realUpdateQYSDKUserInfo:^{
            kQYUserInfoSetting = NO;
        }];
    });
}

- (void)realUpdateQYSDKUserInfo:(dispatch_block_t)completedBlock {
    
    NSString *userId = [IMYPublicAppHelper shareAppHelper].userid;
    NSString *oldUid = self.oldUid;
    
    const BOOL uidValid = userId.imy_isPositiveInt;
    const BOOL oldUidValid = oldUid.imy_isPositiveInt;
    
    if (!uidValid) {
        userId = @"0";
    }
    if (!oldUidValid) {
        oldUid = @"0";
    }
    
#ifdef DEBUG
    // 避免测试uid跟线上用户串线
    if (IMYURLEnvironmentManager.currentType == IMYURLEnviromentTypeTest) {
        userId = [NSString stringWithFormat:@"test-%@", userId];
    }
#endif
    
    id<IMYUpinQYSDK> QYSDK = [self getQYSDK];
    dispatch_block_t setupQYUserInfo = ^{
        NSMutableArray *userData = [NSMutableArray array];
        [userData addObject:@{
            @"key" : @"uid",
            @"label" : @"uid",
            @"value" : userId
        }];
        [userData addObject:@{
            @"key" : @"olduid",
            @"label" : @"olduid",
            @"value" : oldUid
        }];
        [userData addObject:@{
            @"key" : @"nick",
            @"label" : @"nick",
            @"value" : [IMYPublicAppHelper shareAppHelper].nickName ?: @""
        }];
        [userData addObject:@{
            @"key" : @"mode",
            @"label" : @"mode",
            @"value" : @([IMYPublicAppHelper shareAppHelper].userMode).stringValue
        }];
        [userData addObject:@{
            @"key" : @"is_vip",
            @"label" : @"会员身份",
            @"value" : ([[IMYRightsSDK sharedInstance] currentRightsType] != IMYRightsTypeNone) ? @"会员" : @"非会员"
        }];
        [userData addObject:@{
            @"key" : @"myclient",
            @"label" : @"myclient",
            @"value" : [IMYPublicAppHelper shareAppHelper].myclient
        }];
        if (NSBundle.enableMYAppInfo) {
            [userData addObject:@{
                @"key" : @"myappinfo",
                @"label" : @"myappinfo",
                @"value" : [IMYPublicAppHelper shareAppHelper].myappinfo
            }];
        }
        [userData addObject:@{
            @"key" : @"os_v",
            @"label" : @"os_v",
            @"value" : [IMYSystem stringVersion]
        }];
        [userData addObject:@{
            @"key" : @"apn",
            @"label" : @"apn",
            @"value" : [IMYNetState apn]
        }];
        [userData addObject:@{
            @"key" : @"ot",
            @"label" : @"ot",
            @"value" : [UIDevice imy_carrierName]
        }];
        [userData addObject:@{
            @"key" : @"ua",
            @"label" : @"ua",
            @"value" : [UIDevice imy_platform]
        }];
        [userData addObject:@{
            @"key" : @"openudid",
            @"label" : @"openudid",
            @"value" : [UIDevice imy_openUDID]
        }];
        id<IMYUpinQYSDK> userinfo = [NSClassFromString(@"QYUserInfo") new];
        if (uidValid) {
            // uid 有效的情况下，才给七鱼赋值
            userinfo.userId = userId;
        }
        // 赋值其他数据
        userinfo.data = userData.imy_jsonString;
        
        // 由于该七鱼API内部会卡顿，需要在异步执行
        static double kGlobalDelayTime = 1.0;
        [QYSDK setUserInfo:userinfo userInfoResultBlock:^(BOOL success, NSError *error) {
            imy_asyncMainBlock(^{
                if (!success) {
                    // 设置用户信息失败，x秒后再次尝试
                    imy_asyncMainBlock(kGlobalDelayTime, ^{
                        [self updateQYSDKUserInfo];
                    });
                    // 采用指数增加的方式，最多20秒
                    kGlobalDelayTime = MIN(kGlobalDelayTime * 2, 20);
                }
                // 设置完毕
                if (completedBlock) {
                    completedBlock();
                }
            });
        }];
    };
    
    // 当前QY登录UID
    NSString * const currentQYUserID = [QYSDK currentUserID];
    if (currentQYUserID.length > 0 && ![currentQYUserID isEqualToString:userId]) {
        // 已登录 需要先 logout
        [QYSDK logout:^(BOOL success) {
            // 七鱼SDK Bug：对方建议延迟1.5秒后 再设置用户信息，后续版本他们内部修复
            imy_asyncBlock(1.5, setupQYUserInfo);
        }];
    } else {
        // 由于该七鱼API内部会卡顿，跟对方沟通后，可以放到异步执行
        imy_asyncBlock(setupQYUserInfo);
    }
}

// 更新 QYSDK APNS push
- (void)updateApnsToken:(NSData *)deviceToken {
    if (!deviceToken.length) {
        return;
    }
    if (!self.isRegisted) {
        self.deviceToken = deviceToken;
    } else {
        self.deviceToken = nil;
        id<IMYUpinQYSDK> QYSDK = [self getQYSDK];
        [QYSDK updateApnsToken:deviceToken];
    }
}

#pragma mark API

/// 清空所有未读数
- (void)clearAllUnreadCount {
    id conversation = [[self getQYSDK] conversationManager];
    [conversation clearUnreadCount];
}

- (void)clearUnreadCountWithSession:(IMYQYSessionInfo *)session {
    /// 目前还不支持多商家
    [self clearAllUnreadCount];
}

/// 更新七鱼数据
- (void)updateSessions {
    id conversationManager = [[self getQYSDK] conversationManager];
    // 全部未读数
    self.allUnreadCount = [conversationManager allUnreadCount];
    
    // 会话信息
    NSMutableArray<IMYQYSessionInfo *> *mutableArray = [NSMutableArray array];
    NSArray *sessionList = [conversationManager getSessionList];
    for (id session in sessionList) {
        NSString *shopId = [session valueForKey:@"shopId"];
        NSString *avatarImageUrlString = [session valueForKey:@"avatarImageUrlString"];
        NSString *sessionName = [session valueForKey:@"sessionName"];
        NSString *lastMessageText = [session valueForKey:@"lastMessageText"];
        NSInteger unreadCount = [[session valueForKey:@"unreadCount"] integerValue];
        NSTimeInterval lastMessageTimeStamp = [[session valueForKey:@"lastMessageTimeStamp"] doubleValue];
        id lastMessage = [session valueForKey:@"lastMessage"];
        BOOL isReceivedMsg = [[lastMessage valueForKey:@"isReceivedMsg"] boolValue];
        
        IMYQYSessionInfo *info = [IMYQYSessionInfo new];
        info.sessionId = shopId.length > 0 ? shopId : @"0";
        info.sessionName = sessionName.length > 0 ? sessionName : @"客服消息";
        info.sessionIcon = avatarImageUrlString.length > 0 ? avatarImageUrlString : @"https://sc.seeyouyima.com/603377a647d1d_144_144.png";
        info.lastMessageText = lastMessageText ?: @"";
        info.unreadCount = unreadCount;
        info.lastMessageTimeStamp = lastMessageTimeStamp;
        info.actionURI = [IMYURI uriWithPath:@"qiyu/chat" params:nil info:nil].uri;
        info.isReceivedMsg = isReceivedMsg;
        [mutableArray addObject:info];
    }
    self.sessions = mutableArray;
    
    // 发送更新通知
    [(RACSubject *)self.updatedSignal sendNext:self];
}

#pragma mark - QYConversationManagerDelegate

/**
 * 会话未读数变化
 */
- (void)onUnreadCountChanged:(NSInteger)count {
    imy_throttle_on_queue(0.2, @"IMYQYSessionUpdate", dispatch_get_main_queue(), ^{
        [self updateSessions];
    });
}

/**
 * 会话列表变化；非平台电商用户，只有一个会话项，平台电商用户，有多个会话项
 */
- (void)onSessionListChanged:(NSArray *)sessionList {
    imy_throttle_on_queue(0.2, @"IMYQYSessionUpdate", dispatch_get_main_queue(), ^{
        [self updateSessions];
    });
}

/**
 *  接收消息
 */
- (void)onReceiveMessage:(id)message {
    imy_throttle_on_queue(0.2, @"IMYQYSessionUpdate", dispatch_get_main_queue(), ^{
        [self updateSessions];
    });
}

/**
 *  会话列表变化
 */
- (void)onSessionListChanged {
    imy_throttle_on_queue(0.2, @"IMYQYSessionUpdate", dispatch_get_main_queue(), ^{
        [self updateSessions];
    });
}

@end


@implementation IMYQYSessionInfo

@end

#pragma mark - QY PUSH

@interface IMYQYSessionAppDelegate : IMYAppBaseDelegate <UIApplicationDelegate>

@end

@implementation IMYQYSessionAppDelegate

// revice push token
- (void)application:(UIApplication *)application didRegisterForRemoteNotificationsWithDeviceToken:(NSData *)deviceToken {
    imy_asyncMainBlockInDefaultMode(1, ^{
        [[IMYQYSessionManager sharedInstance] updateApnsToken:deviceToken];
    });
}

// push click
- (void)userNotificationCenter:(UNUserNotificationCenter *)center didReceiveNotificationResponse:(UNNotificationResponse *)response withCompletionHandler:(void (^)(void))completionHandler  API_AVAILABLE(ios(10.0)){
    // 只处理 七鱼push
    NSDictionary *userInfo = response.notification.request.content.userInfo;
    if ([userInfo[@"nim"] integerValue] != 1) {
        return;
    }
    // 七鱼Push，直接跳转到客服页面即可
    [[IMYPublicAppHelper shareAppHelper] runActionBlockWithLaunchFinished:^{
        [[IMYURIManager shareURIManager] runActionWithPath:@"qiyu/chat" params:nil info:nil];
    } forKey:@"QYPush"];
}

@end

@protocol QAIMYAdSplashManager
// IMYAdSplashManager
+ (id)shareInstance;
///闪屏广告的状态
- (NSInteger)showADSplashType;
@end

static BOOL IMYInAdSplash(void) {
    ///未知
    const NSInteger IMYADSplashTypeNone = 0;
    ///请求中
    const NSInteger IMYADSplashTypeLoading = 1;
    ///显示中
    const NSInteger IMYADSplashTypeShowing = 2;
    ///没有显示
    const NSInteger IMYADSplashTypeNoneDisplay = 3;
    ///显示完毕
    const NSInteger IMYADSplashTypeDismissed = 4;
    
    // 广告开屏&插屏状态
    id adSplash = [NSClassFromString(@"IMYAdSplashManager") shareInstance];
    
    //闪屏广告
    const NSInteger adSplashType = [adSplash showADSplashType];
    if (!adSplash ||
        adSplashType == IMYADSplashTypeNoneDisplay ||
        adSplashType == IMYADSplashTypeDismissed) {
        // 无闪屏广告，继续判断其它情况
        return NO;
    }
    return YES;
}

static void IMYGotoQiyuChat(IMYURIActionBlockObject *actionObject) {
    if (IMYInAdSplash() || ![IMYPublicAppHelper shareAppHelper].hasAgreedPrivacy) {
        // 如果正在显示开屏广告，则过1秒后再次判断
        imy_asyncMainBlock(1, ^{
            IMYGotoQiyuChat(actionObject);
        });
        return;
    }
    if (![IMYQYSessionManager sharedInstance].isRegisted) {
        // 如果七鱼SDK还未初始化化，则先初始化在进行跳转
        [[IMYQYSessionManager sharedInstance] setupQYSDK];
        imy_asyncMainBlock(0.2, ^{
            IMYGotoQiyuChat(actionObject);
        });
        return;
    }
    
    // 页面title
    NSString *title = actionObject.uri.params[@"title"];
    // 来源信息
    NSString *sourceUrl = actionObject.uri.params[@"sourceUrl"];
    NSString *sourceTitle = actionObject.uri.params[@"sourceTitle"];
    NSString *sourceExtra = actionObject.uri.params[@"sourceExtra"];
    // 客服分组id
    NSInteger groupId = [actionObject.uri.params[@"groupId"] integerValue];
    NSInteger staffId = [actionObject.uri.params[@"staffId"] integerValue];
    // 机器人id
    NSInteger robotId = [actionObject.uri.params[@"robotId"] integerValue];
    
    // 默认添加机器人id
    if (robotId == 0) {
        robotId = 3490518;
    }
    
    // 当前显示的VC 已经是七鱼客服，则不在push新页面
    NSArray *showVCs = actionObject.getUsingViewController.imy_navigationController.viewControllers;
    if ([showVCs.lastObject isKindOfClass:NSClassFromString([IMYQYSessionManager getQYSessionViewControllerClassName])]) {
        return;
    }
    
    // 用户uid
    if (actionObject.uri.params[@"uid"]) {
        [IMYQYSessionManager sharedInstance].oldUid = actionObject.uri.params[@"uid"];
    }
    
    UIViewController *sessionViewController = [[IMYQYSessionManager sharedInstance] getQYSessionViewController];
    
    // 没有头文件， 都使用 KVC 进行赋值
    id source = [NSClassFromString(@"QYSource") new];
    [source setValue:sourceTitle forKey:@"title"];
    [source setValue:sourceUrl forKey:@"urlString"];
    [source setValue:sourceExtra forKey:@"customInfo"];
    
    [sessionViewController setValue:title forKey:@"sessionTitle"];
    [sessionViewController setValue:source forKey:@"source"];
    if (groupId > 0) {
        [sessionViewController setValue:@(groupId) forKey:@"groupId"];
    }
    if (staffId > 0) {
        [sessionViewController setValue:@(staffId) forKey:@"staffId"];
    }
    if (robotId > 0) {
        [sessionViewController setValue:@(robotId) forKey:@"robotId"];
    }
    // 开启机器人分流
    [sessionViewController setValue:@YES forKey:@"openRobotInShuntMode"];
    
    // 七鱼连接前，无法进行输入
//    if ([sessionViewController respondsToSelector:@selector(isForbiddenInput)]) {
//        [sessionViewController setValue:@YES forKey:@"isForbiddenInput"];
//    }
    
    // 七鱼背景是白色的，需要 返回按钮 和 statusbar 都使用黑色
    [sessionViewController imy_topLeftButtonIsBack];
    if (@available(iOS 13.0, *)) {
        sessionViewController.imy_config.preferredStatusBarStyle = UIStatusBarStyleDarkContent;
    } else {
        sessionViewController.imy_config.preferredStatusBarStyle = UIStatusBarStyleDefault;
    }
    [actionObject.getUsingViewController imy_push:sessionViewController];
    // 打开侧滑返回
    sessionViewController.navigationController.interactivePopGestureRecognizer.enabled = YES;
}

IMY_KYLIN_FUNC_PREMAIN {
    // 添加七鱼Push监听
    IMYRegisterModule([IMYQYSessionAppDelegate class]);
    
    //  启动七鱼客服聊天页面
    [[IMYURIManager shareURIManager] addForPath:@"qiyu/chat" withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        // 由于会进行递归调用，抽离到外部
        IMYGotoQiyuChat(actionObject);
    }];
}
